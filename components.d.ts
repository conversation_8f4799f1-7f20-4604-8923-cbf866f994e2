/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    CustomModal: typeof import('./src/components/CustomModal/CustomModal.vue')['default']
    CustomNavBar: typeof import('./src/components/CustomNavBar/CustomNavBar.vue')['default']
    NutCalendarCard: typeof import('@nutui/nutui-taro')['CalendarCard']
    NutEmpty: typeof import('@nutui/nutui-taro')['Empty']
    NutInput: typeof import('@nutui/nutui-taro')['Input']
    NutPopup: typeof import('@nutui/nutui-taro')['Popup']
    NutRadio: typeof import('@nutui/nutui-taro')['Radio']
    NutRadioGroup: typeof import('@nutui/nutui-taro')['RadioGroup']
    NutTextarea: typeof import('@nutui/nutui-taro')['Textarea']
  }
}
