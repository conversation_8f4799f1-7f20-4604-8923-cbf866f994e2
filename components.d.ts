/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    CustomModal: typeof import('./src/components/CustomModal/CustomModal.vue')['default']
    CustomNavBar: typeof import('./src/components/CustomNavBar/CustomNavBar.vue')['default']
    NutButton: typeof import('@nutui/nutui-taro')['Button']
    NutCalendarCard: typeof import('@nutui/nutui-taro')['CalendarCard']
    NutCascader: typeof import('@nutui/nutui-taro')['Cascader']
    NutEmpty: typeof import('@nutui/nutui-taro')['Empty']
    NutInput: typeof import('@nutui/nutui-taro')['Input']
    NutPicker: typeof import('@nutui/nutui-taro')['Picker']
    NutPopup: typeof import('@nutui/nutui-taro')['Popup']
    NutRadio: typeof import('@nutui/nutui-taro')['Radio']
    NutRadioGroup: typeof import('@nutui/nutui-taro')['RadioGroup']
    NutStep: typeof import('@nutui/nutui-taro')['Step']
    NutSteps: typeof import('@nutui/nutui-taro')['Steps']
    NutSwitch: typeof import('@nutui/nutui-taro')['Switch']
    NutTag: typeof import('@nutui/nutui-taro')['Tag']
    NutTextarea: typeof import('@nutui/nutui-taro')['Textarea']
  }
}
