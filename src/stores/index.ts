import { getStore } from '@/utils/utils';
import { defineStore } from 'pinia';

// 定义 auth 模块
export const useAuthStore = defineStore('auth', {
	state: () => ({
		token: '',
	}),
	actions: {
		setToken(token: string) {
			this.token = token;
		},
		clearToken() {
			this.token = '';
		},
		getToken() {
			return this.token;
		},
		isLogin() {
			return this.token !== '';
		},
	},
});
// 定义 user 模块
export const useUserStore = defineStore('user', {
	state: () => ({
		userInfo: null,
	}),
	actions: {
		setUserInfo(userInfo: any) {
			this.userInfo = userInfo;
		},
		clearUserInfo() {
			this.userInfo = null;
		},
		getUserInfo() {
			return this.userInfo;
		},
		isLogin() {
			return this.userInfo !== null;
		},
	},
});
// 初始化登录信息
export const initStore = () => {
	const authStore = useAuthStore();
	const userStore = useUserStore();
	const token = getStore('token');
	const userInfo = getStore('userInfo');
	console.log('初始化登录信息：token:', token, 'userInfo:', userInfo);
	if (token) {
		authStore.setToken(token);
	}
	if (userInfo) {
		userStore.setUserInfo(userInfo);
	}
};

// 定义园区模块
export const useAreaStore = defineStore('currentArea', {
	state: () => ({
		currentArea: null,
	}),
	actions: {
		setCurrentArea(area: any) {
			this.currentArea = area;
		},
		clearCurrentArea() {
			this.currentArea = null;
		},
		getCurrentArea() {
			return this.currentArea;
		},
	},
});

// 定义工单模块
export const useWorkOrderStore = defineStore('undoWorkOrder', {
	state: () => ({
		undoWorkOrderCount: 0,
	}),
	actions: {
		setUndoWorkOrderCount(undoWorkOrderCount: any) {
			this.undoWorkOrderCount = undoWorkOrderCount;
		},
		clearUndoWorkOrderCount() {
			this.undoWorkOrderCount = 0;
		},
		getUndoWorkOrderCount() {
			return this.undoWorkOrderCount;
		},
	},
});

// 定义测试计数器
export const demoTestCounterStore = defineStore('counter', {
	state: () => {
		return { count: 0 };
	},
	actions: {
		increment() {
			this.count++;
		},
	},
});
