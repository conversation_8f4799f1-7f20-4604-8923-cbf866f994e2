<template>
	<view class="tenant">
		<view class="visit-header">
			<view class="visit-search-box">
				<view class="search-box">
					<Search color="#ccc" />
					<NutInput
						class="search-box-input"
						v-model="searchText"
						clearable
						clear-size="16"
						show-clear-icon
						:border="false"
						placeholder="请输入姓名或者手机号"
					/>
					<view class="search-box-btn" @click="getTenantList()">搜索</view>
				</view>
			</view>
			<view class="filter-box">
				<view class="filter-box-date" @click="isPickerModalShow = true">
					<Date size="11" color="#ff6b35" />
					<text>
						{{
							dateRangeList.find((item) => item.value === dateStr)?.text || '全部时间'
						}}
					</text>
				</view>
				<view class="filter-box-reset" @click="onResetHandler">
					<Refresh color="grey" size="13" />
					<text>重置</text>
				</view>
			</view>
		</view>
		<view class="tenant-list-content" v-if="tenantList.length > 0 || isLoading">
			<view
				class="tenant-list-content-item"
				v-for="item in tenantList"
				:key="item.contractId"
				@click="onItemClickHandler(item)"
			>
				<view class="header">
					<view class="title">{{ item.partyTwoName }}（房主）</view>
					<view class="status" :class="`state_${tenantState(item).value}`">
						{{ tenantState(item).text }}
					</view>
				</view>
				<view class="location">
					<Location2 size="13" color="#397fef" />
					<text>
						{{ item?.term }}期 {{ item?.buildingNo }}幢 {{ item?.unit }}单元
						{{ item?.gatehouseNo }}室
					</text>
				</view>
				<view class="time">
					<text>
						入住周期：{{ item.contractStartTime?.split(' ')[0] }} -
						{{ item.contractEndTime?.split(' ')[0] }}
					</text>
					<text>
						已住
						<text class="time-date">{{ getTenantStayDays(item) }}</text>
						天
					</text>
				</view>
			</view>
			<view class="tenant-list-content-item-more" @click="onLoadMore">
				<template v-if="isLoading">
					<Loading1 size="20" style="color: #397fef; font-weight: bold" />
					<text>加载中...</text>
				</template>
				<template v-else>
					<text v-if="isMoreData">加载更多</text>
					<text v-else>没有更多了~</text>
				</template>
			</view>
		</view>
		<nut-empty v-else class="tenant-list-content-empty" description="暂无房客数据"></nut-empty>
		<NutPopup v-model:visible="isPickerModalShow" position="bottom">
			<NutPicker
				:columns="dateRangeList"
				@confirm="pickerConfirm"
				@cancel="isPickerModalShow = false"
			/>
		</NutPopup>
		<!-- 浮动按钮 -->
		<view class="more_btn" @click="onRefundRentHandler">
			<image class="more_btn_image" src="@/assets/images/refund_rent.png" mode="widthFix" />
		</view>
	</view>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import './index.scss';
import { useAreaStore } from '@/stores';
import {
	getWeekStart,
	getWeekEnd,
	getMonthStart,
	getMonthEnd,
	getQuarterStart,
	getQuarterEnd,
	currentTimestamp,
	dateTimeToTimestamp,
} from '@/utils/utils';
import { jwAppApi_V1_Contract_PagePartyTwo_GET } from '@/utils/api';
import Taro from '@tarojs/taro';

// 引入资源
import { Search, Refresh, Date, Loading1, Location2 } from '@nutui/icons-vue-taro';

// 使用 Pinia store
const areaStore = useAreaStore();
const currentArea = computed(() => areaStore.getCurrentArea());

onMounted(() => {
	getTenantList();
});

// 房客列表
const tenantList: any = ref([]);

// 获取房客列表
const getTenantList = async () => {
	if (!isRefreshing.value) {
		isLoading.value = true;
	}
	try {
		const params: any = {
			areaCode: currentArea.value?.areaId,
			pageNum: 1,
			pageSize: pageSize * currentPage.value,
			nameOrPhoneNumber: searchText.value,
		};
		if (dateStr.value) {
			params.contractStartTime = dateRange.value[0];
			params.contractEndTime = dateRange.value[1];
		}
		const res = await jwAppApi_V1_Contract_PagePartyTwo_GET(params);
		console.log('房客列表:', res);
		tenantList.value = res.items;
		total.value = res.total;
	} catch (error) {
		console.error('获取房客列表失败:', error);
	} finally {
		isLoading.value = false;
	}
};

// 搜索文本
const searchText = ref('');

const pageSize = 20;
const currentPage = ref(1);
const total = ref(0);

// 加载更多
const isLoading = ref(false);
const isMoreData = computed(() => {
	return currentPage.value * pageSize < total.value;
});
// 加载更多
const onLoadMore = () => {
	if (currentPage.value * pageSize >= total.value) {
		return;
	}
	currentPage.value++;
	getTenantList();
};

const isRefreshing = ref(false);
// 下拉刷新处理
const onRefresh = async (isReloadPage: boolean = false) => {
	console.log('开始下拉刷新');
	currentPage.value = 1;
	isRefreshing.value = true;
	try {
		await new Promise((resolve) => setTimeout(resolve, 500));
		await getTenantList();
		if (!isReloadPage) {
			Taro.showToast({
				title: '刷新成功',
				icon: 'success',
			});
		}
	} catch (error) {
		console.error('下拉刷新失败:', error);
		Taro.showToast({
			title: '刷新失败',
			icon: 'error',
		});
	} finally {
		isRefreshing.value = false;
		Taro.stopPullDownRefresh();
	}
};

// 使用 Taro 的下拉刷新 hook
Taro.usePullDownRefresh(onRefresh);

// 重置筛选
const onResetHandler = () => {
	searchText.value = '';
	dateStr.value = undefined;
	getTenantList();
};

const onRefundRentHandler = () => {
	Taro.navigateTo({
		url: '/pagesB/refundRent/index',
	});
};

const onItemClickHandler = (item: any) => {
	Taro.navigateTo({
		url: `/pagesB/tenantDetail/index?contractId=${item.contractId}&houseId=${item.houseId}`,
	});
};

const isPickerModalShow = ref(false);

const pickerConfirm = ({ selectedOptions }: any) => {
	dateStr.value = selectedOptions[0].value;
	switch (dateStr.value) {
		case 1:
			dateRange.value = [getWeekStart(), getWeekEnd()];
			break;
		case 2:
			dateRange.value = [getMonthStart(), getMonthEnd()];
			break;
		case 3:
			dateRange.value = [getQuarterStart(), getQuarterEnd()];
			break;
		default:
			break;
	}
	isPickerModalShow.value = false;
	getTenantList();
};

// 时间范围
const dateStr: any = ref(undefined);
const dateRange: any = ref([]);
const dateRangeList = ref([
	{ text: '全部时间', value: undefined },
	{ text: '本周', value: 1 },
	{ text: '本月', value: 2 },
	{ text: '本季度', value: 3 },
]);

const tenantState = (item: any) => {
	// 判断当前时间与 item.contractStartTime 的关系
	// 1.如果大于 item.contractStartTime 且小于 item.contractEndTime，则为在住
	// 2.如果小于 item.contractStartTime，则为待入住
	// 3.如果大于 item.contractEndTime，则为已离开
	const currentTime = currentTimestamp();
	if (
		currentTime > dateTimeToTimestamp(item.contractStartTime) &&
		currentTime < dateTimeToTimestamp(item.contractEndTime)
	) {
		return { value: 1, text: '入住中' };
	} else if (currentTime < dateTimeToTimestamp(item.contractStartTime)) {
		return { value: 2, text: '待入住' };
	} else if (currentTime > dateTimeToTimestamp(item.contractEndTime)) {
		return { value: 3, text: '已迁出' };
	}
	return { value: 2, text: '待入住' };
};

const getTenantStayDays = (item: any) => {
	const currentTime = currentTimestamp();
	if (currentTime > dateTimeToTimestamp(item.contractStartTime)) {
		const start = dateTimeToTimestamp(item.contractStartTime);
		const end = Math.min(currentTime, dateTimeToTimestamp(item.contractEndTime));
		const diff = end - start;
		return Math.floor(diff / (1000 * 60 * 60 * 24));
	}
	return 0;
};
</script>
