.tenant {
	height: 100%;
	// background-color: #fff;
	.visit-header {
		.visit-search-box {
			padding: 0 20px;
			background-color: #fff;
			.search-box {
				width: 100%;
				height: 80px;
				padding: 0 20px;
				border-radius: 15px;
				box-sizing: border-box;
				border: 1px solid #ccc;
				display: flex;
				align-items: center;
				justify-content: center;
				gap: 20px;
				.search-box-input {
					flex: 1;
				}
				.search-box-btn {
					color: #397fef;
					font-size: 30px;
					position: relative;
					width: 90px;
					margin-left: 20px;
					&::before {
						content: '|';
						position: absolute;
						color: #ccc;
						left: -30px;
						top: 50%;
						transform: translateY(-50%);
					}
				}
			}
		}
		.filter-box {
			width: 100%;
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 10px 20px;
			font-size: 24px;
			box-sizing: border-box;
			background-color: #fff;
			.filter-box-date {
				display: flex;
				align-items: center;
				justify-content: center;
				gap: 10px;
				color: #ff6b35;
				padding: 5px 15px;
				background-color: #f0f5fa;
				border-radius: 10px;
			}
			.filter-box-reset {
				display: flex;
				align-items: center;
				justify-content: center;
				gap: 10px;
				color: grey;
				padding: 5px 15px;
				background-color: #f0f5fa;
				border-radius: 10px;
			}
		}
	}
	.tenant-list-content {
		flex: 1;
		overflow-y: auto;
		padding: 0 30px;
		box-sizing: border-box;
		display: flex;
		flex-direction: column;
		gap: 20px;
		.tenant-list-content-item {
			border-bottom: 2px solid #f5f6f7;
			padding: 10px 0 30px 0;
			display: flex;
			flex-direction: column;
			gap: 10px;
			text {
				font-size: 28px;
				color: #333;
			}
      .header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				.title {
					font-size: 30px;
					font-weight: bold;
				}
				.status {
					font-size: 28px;
					&.state_0 {
						color: #f44336;
					}
					&.state_1 {
						color: #4caf50;
					}
					&.state_2 {
						color: #9e9e9e;
					}
					&.state_-1 {
						color: #9e9e9e;
					}
				}
			}
		}
		.tenant-list-content-item-more {
			width: 100%;
			box-sizing: border-box;
			padding: 10px;
			display: flex;
			justify-content: center;
			align-items: center;
			gap: 20px;
			font-size: 28px;
			color: #333;
		}
	}

	.tenant-list-content-empty {
		text-align: center;
		padding: 200px 0;
		font-size: 32px;
		color: #999;
	}
	// 浮动按钮
	.more_btn {
		position: fixed;
		right: 30px;
		bottom: 100px;
		width: 100px;
		height: 100px;
		border-radius: 50%;
		background-color: #f44336;
		box-shadow: 10px 10px 20px rgba(180, 180, 180, 0.8);
		display: flex;
		align-items: center;
		z-index: 1000;
		.more_btn_image {
			width: 60%;
			margin: auto;
		}
	}

	// nut的input输入框
	.nut-input {
		padding: 0;
		margin: 0;
	}
	.input-text {
		color: #333;
		font-size: 30px;
	}
	// 设置 NutUI 组件的 CSS 变量
	--nut-picker-confirm-color: #397fef;
	--nut-primary-color: #397fef;
}
