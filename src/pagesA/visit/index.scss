.visit {
	background-color: #fff;
	min-height: 100vh;
	.visit-header {
		.visit-search-box {
			padding: 0 20px;
			background-color: #f5f6f7;
			.search-box {
				width: 100%;
				height: 80px;
				padding: 0 20px;
				border-radius: 15px;
				box-sizing: border-box;
				border: 1px solid #ccc;
				display: flex;
				align-items: center;
				justify-content: center;
				gap: 20px;
				.search-box-input {
					flex: 1;
				}
				.search-box-btn {
					color: #397fef;
					font-size: 30px;
					position: relative;
					width: 90px;
					margin-left: 20px;
					&::before {
						content: '|';
						position: absolute;
						color: #ccc;
						left: -30px;
						top: 50%;
						transform: translateY(-50%);
					}
				}
			}
		}
		.visit-date-box {
			display: flex;
			height: 80px;
			background-color: #f5f6f7;
			.isSelected {
				font-weight: bold;
				color: #397fef;
			}
			.date-all {
				height: 100%;
				padding: 0 40px;
				display: flex;
				align-items: center;
				justify-content: center;
			}
			.date-scroll::-webkit-scrollbar {
				display: none;
				height: 0; /* 水平滚动条高度设为 0 */
				width: 0; /* 垂直滚动条宽度设为 0（如果不需要可省略） */
			}

			.date-scroll {
				flex: 1;
				width: 100%;
				height: 100%;
				white-space: nowrap;
				overflow-x: auto;
				overflow-y: hidden;
				-webkit-overflow-scrolling: touch;
				scrollbar-width: none; /* 标准属性，隐藏滚动条（支持现代浏览器） */
				-ms-overflow-style: none; /* IE 和 Edge 隐藏滚动条 */
				.date-list {
					display: inline-flex;
					align-items: center;
					height: 100%;
					.date-item {
						height: 100%;
						line-height: 80px;
						padding: 0 50px;
						position: relative;
						.badge {
							position: absolute;
							top: 15px;
							right: 35px;
							width: 12px;
							height: 12px;
							background-color: #ff4444;
							border-radius: 50%;
						}
					}
				}
			}
		}
		.filter-box {
			width: 100%;
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 10px 20px;
			font-size: 24px;
			box-sizing: border-box;
			.filter-box-date {
				display: flex;
				align-items: center;
				justify-content: center;
				gap: 10px;
				color: #ff6b35;
				padding: 5px 15px;
				background-color: #f5f6f7;
				border-radius: 10px;
			}
			.filter-box-reset {
				display: flex;
				align-items: center;
				justify-content: center;
				gap: 10px;
				color: grey;
				padding: 5px 15px;
				background-color: #f5f6f7;
				border-radius: 10px;
			}
		}
	}
	.visit-list-content {
		flex: 1;
		overflow-y: auto;
		padding: 0 30px;
		box-sizing: border-box;
		display: flex;
		flex-direction: column;
		gap: 20px;

		.visit-list-content-item {
			border-bottom: 2px solid #f5f6f7;
			padding: 10px 0 30px 0;
			display: flex;
			flex-direction: column;
			gap: 10px;
			text {
				font-size: 28px;
				color: #333;
			}
			.header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				.title {
					font-size: 30px;
					font-weight: bold;
				}
				.status {
					font-size: 28px;
					&.state_0 {
						color: #ff6b35;
					}
					&.state_1 {
						color: #4caf50;
					}
					&.state_2 {
						color: #9e9e9e;
					}
					&.state_-1 {
						color: #9e9e9e;
					}
				}
			}

			.item-row {
				display: flex;
				align-items: center;
				justify-content: space-between;
				.label {
					font-size: 28px;
					color: #333;
					margin-right: 8px;
				}
				.value {
					font-size: 28px;
					color: #666;
					flex: 1;
					display: flex;
					justify-content: flex-end;
				}
			}
			.item-actions {
				display: flex;
				justify-content: flex-end;
				gap: 20px;
				// background-color: pink;
				.cancel-visit {
					font-size: 30px;
					.cancel-text {
						color: #fff;
						padding: 10px 20px;
						border-radius: 10px;
						background-color: #ff4444;
						display: inline-block;
						min-width: 80px;
						text-align: center;
					}
				}
				.complete-visit {
					font-size: 30px;
					.complete-text {
						color: #fff;
						padding: 10px 20px;
						border-radius: 10px;
						background-color: #397fef;
						display: inline-block;
						min-width: 80px;
						text-align: center;
					}
				}
			}
		}
		.visit-list-content-item-more {
			width: 100%;
			box-sizing: border-box;
			padding: 10px;
			display: flex;
			justify-content: center;
			align-items: center;
			gap: 20px;
			font-size: 28px;
			color: #333;
		}
	}

	.visit-list-content-empty {
		text-align: center;
		padding: 200px 0;
		font-size: 32px;
		color: #999;
	}

	// nut的input输入框
	.nut-input {
		padding: 0;
		margin: 0;
	}
	.input-text {
		color: #333;
		font-size: 30px;
	}
	.nut-input-inner {
		background-color: #f5f6f7;
	}

	// 设置 NutUI 组件的 CSS 变量
	--nut-picker-confirm-color: #397fef;
	--nut-primary-color: #397fef;
}
