<template>
	<view class="visit">
		<view class="visit-header">
			<view class="visit-search-box">
				<view class="search-box">
					<Search color="#ccc" />
					<NutInput
						class="search-box-input"
						v-model="searchText"
						clearable
						clear-size="16"
						show-clear-icon
						:border="false"
						placeholder="请输入姓名或者手机号"
					/>
					<view class="search-box-btn" @click="getVisitList(false)">搜索</view>
				</view>
			</view>
			<view class="visit-date-box">
				<view
					class="date-all"
					@click="onDateClickHandler(null)"
					:class="{ isSelected: !currentDate }"
				>
					全部
				</view>
				<view class="date-scroll">
					<view class="date-list">
						<view
							class="date-item"
							v-for="item in dateList"
							:key="item.value"
							@click="onDateClickHandler(item)"
						>
							<view v-if="item.isBadge" class="badge"></view>
							<text :class="{ isSelected: item.value === currentDate }">
								{{ item.text }}
							</text>
						</view>
					</view>
				</view>
			</view>
			<view class="filter-box">
				<view class="filter-box-date" @click="isPickerModalShow = true">
					<Category size="11" color="#ff6b35" />
					<text>
						{{
							visitStatusList.find((item) => item.value === visitStatus)?.text ||
							'全部约看'
						}}
					</text>
				</view>
				<view class="filter-box-reset" @click="onResetHandler">
					<Refresh color="grey" size="13" />
					<text>重置</text>
				</view>
			</view>
		</view>
		<view class="visit-list-content" v-if="visitList.length > 0 || isLoading">
			<view
				class="visit-list-content-item"
				v-for="item in visitList"
				:key="item.id"
				@click="onItemClickHandler(item)"
			>
				<view class="header">
					<view class="title">{{ item.visitorName }}</view>
					<view class="status" :class="`state_${visitState(item.examineStatus).value}`">
						{{ visitState(item.examineStatus).text }}
					</view>
				</view>
				<view class="item-row">
					<view class="label">意向房型</view>
					<text class="value">{{ item?.houseType?.name || '未知' }}</text>
				</view>
				<view class="item-row">
					<view class="label">来访时间</view>
					<view class="value">{{ item?.visitDate }} 全天(08:30-17:30)</view>
				</view>
				<view class="item-row">
					<view class="label">联系电话</view>
					<view
						class="value"
						style="color: #397fef"
						@click.stop="onCallHandler(item.visitorPhone)"
					>
						{{ item?.visitorPhone }}
					</view>
				</view>
				<view class="item-actions" v-if="item.examineStatus === 0">
					<view class="cancel-visit">
						<view class="cancel-text" @click.stop="onCancelVisitClick(item)">
							取消预约
						</view>
					</view>
					<view class="complete-visit">
						<view class="complete-text" @click.stop="onCompleteVisitClick(item)">
							完成预约
						</view>
					</view>
				</view>
			</view>
			<view class="visit-list-content-item-more" @click="onLoadMore">
				<template v-if="isLoading">
					<Loading1 size="20" style="color: #397fef; font-weight: bold" />
					<text>加载中...</text>
				</template>
				<template v-else>
					<text v-if="isMoreData">加载更多</text>
					<text v-else>没有更多了~</text>
				</template>
			</view>
		</view>
		<nut-empty v-else class="visit-list-content-empty" description="暂无来访数据"></nut-empty>
		<NutPopup v-model:visible="isPickerModalShow" position="bottom">
			<NutPicker
				:columns="visitStatusList"
				@confirm="pickerConfirm"
				@cancel="isPickerModalShow = false"
			/>
		</NutPopup>
		<CustomModal
			v-model:visible="cancelVisitModalConfig.isShow"
			:title="cancelVisitModalConfig.title"
			:content="cancelVisitModalConfig.content"
			:confirm-text="cancelVisitModalConfig.confirmText"
			:cancel-text="cancelVisitModalConfig.cancelText"
			@confirm="onCancelVisitConfirm"
		/>
		<CustomModal
			v-model:visible="completeVisitModalConfig.isShow"
			:title="completeVisitModalConfig.title"
			:content="completeVisitModalConfig.content"
			:confirm-text="completeVisitModalConfig.confirmText"
			:cancel-text="completeVisitModalConfig.cancelText"
			@confirm="onCompleteVisitConfirm(true)"
			@cancel="onCompleteVisitConfirm(false)"
		/>
	</view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, Ref } from 'vue';
import './index.scss';
import { useAreaStore } from '@/stores';
import { formatDate } from '@/utils/utils';
import {
	jwCloudPedestal_V1_VisitorsReservation_Page_GET,
	jwCloudPedestal_V1_VisitorsReservation_Examine_POST,
} from '@/utils/api';
import Taro from '@tarojs/taro';

// 引入资源
import { Search, Refresh, Category, Loading1 } from '@nutui/icons-vue-taro';

// 使用 Pinia store
const areaStore = useAreaStore();
const currentArea = computed(() => areaStore.getCurrentArea());

onMounted(() => {
	// 初始化日期列表
	initDateList();
	getVisitList();
});

// 当前选中的日期
const currentDate = ref('');

// 日期列表
const dateList: Ref<any[]> = ref([]);
const initDateList = () => {
	// 初始化日期列表，从今天开始近7天, 格式为yyyy-MM-dd
	const now = new Date();
	const dates: any = [];
	for (let i = 0; i < 7; i++) {
		const date = new Date(now);
		date.setDate(now.getDate() + i);
		dates.push({
			text: i === 0 ? '今天' : formatDate(date, 'DD'),
			value: formatDate(date, 'YYYY-MM-DD'),
		});
	}
	dateList.value = dates;
	console.log('日期列表:', dateList.value);
};
// 格式化日期消息，是否有消息
const onFormatDateMessage = () => {
	console.log('visitListData：', visitListData.value);
	const visitDates = visitListData.value.map((item: any) => {
		return {
			visitDate: item.visitDate,
			examineStatus: item.examineStatus,
		};
	});
	dateList.value.forEach((item) => {
		const index = visitDates.findIndex((visit: any) => visit.visitDate === item.value);
		if (index !== -1) {
			item.isBadge = visitDates[index].examineStatus === 0;
		} else {
			item.isBadge = false;
		}
	});
};

// 点击日期
const onDateClickHandler = (item: any) => {
	if (!item) {
		currentDate.value = '';
	} else {
		// 如果点击的是今天，直接返回
		currentDate.value = item.value;
	}
	getVisitList(item ? false : true);
};

// 搜索文本
const searchText = ref('');

const pageSize = 20;
const currentPage = ref(1);
const total = ref(0);

// 加载更多
const isLoading = ref(false);
const isMoreData = computed(() => {
	return currentPage.value * pageSize < total.value;
});
// 加载更多
const onLoadMore = () => {
	if (currentPage.value * pageSize >= total.value) {
		return;
	}
	currentPage.value++;
	getVisitList();
};

// 重置筛选
const onResetHandler = () => {
	currentDate.value = '';
	searchText.value = '';
	visitStatus.value = undefined;
	getVisitList();
};

const visitList: any = ref([]);
const visitListData = ref([]);
const getVisitList = async (isRefresh: boolean = true) => {
	if (!isRefreshing.value) {
		isLoading.value = true;
	}
	await new Promise((resolve) => setTimeout(resolve, 500));
	const params: any = {
		pageNum: 1,
		pageSize: pageSize * currentPage.value,
		areaId: currentArea.value?.areaId,
	};
	if (visitStatus.value) {
		params.examineStatus = visitStatus.value;
	}
	if (currentDate.value) {
		params.visitDateStart = currentDate.value;
		params.visitDateEnd = currentDate.value;
	} else {
		params.visitDateStart = dateList.value[0].value;
		params.visitDateEnd = dateList.value[dateList.value.length - 1].value;
	}

	// 需要校验文本内容，如果是纯数字属性则为手机号，否则为姓名
	if (searchText.value) {
		if (/^\d+$/.test(searchText.value)) {
			params.visitorPhone = searchText.value;
		} else {
			params.visitorName = searchText.value;
		}
	}

	console.log('params', params);
	try {
		const response = await jwCloudPedestal_V1_VisitorsReservation_Page_GET(params);
		console.log('response:', response);
		visitList.value = response?.items || [];
		if (isRefresh) {
			visitListData.value = response?.items || [];
			// 格式化日期消息
			onFormatDateMessage();
		}
		total.value = response.total;
	} catch (error) {
		console.error('获取列表失败:', error);
	} finally {
		isLoading.value = false;
	}
};

const isPickerModalShow = ref(false);

const pickerConfirm = ({ selectedOptions }: any) => {
	visitStatus.value = selectedOptions[0].value;
	isPickerModalShow.value = false;
};

// 访问状态
const visitStatus = ref(undefined);
const visitStatusList = ref([
	{
		text: '全部约看',
		value: undefined,
	},
	{
		text: '待约看',
		value: 0,
	},
	{
		text: '已完成',
		value: 1,
	},
	{
		text: '已取消',
		value: 2,
	},
]);

// 0-待指派,1-待处理,2-已处理
const visitState = (state: number) => {
	switch (state) {
		case 0:
			return { value: 0, text: '待约看' };
		case 1:
			return { value: 1, text: '已完成' };
		case 2:
			return { value: 2, text: '已取消' };
		default:
			return { value: -1, text: '未知' };
	}
};

const onItemClickHandler = (item: any) => {
	Taro.navigateTo({
		url: '/pagesB/visitDetail/index?id=' + item.id,
	});
};

// 拨打电话
const onCallHandler = (phone: string) => {
	Taro.makePhoneCall({
		phoneNumber: phone,
	})
		.then(() => {
			console.log('拨打电话成功');
		})
		.catch(() => {
			console.log('拨打电话失败');
		});
};

const onCancelVisitClick = (item: any) => {
	cancelVisitModalConfig.value.isShow = true;
	cancelVisitModalConfig.value.item = item;
};
const onCompleteVisitClick = (item: any) => {
	completeVisitModalConfig.value.isShow = true;
	completeVisitModalConfig.value.item = item;
};

// ========== 自定义 Modal 相关方法 ==========
const cancelVisitModalConfig: any = ref({
	isShow: false,
	title: '取消预约',
	content: '确认取消预约吗？',
	confirmText: '确定取消',
	cancelText: '我再想想',
	item: null, // 用于存储当前操作的预约项
});
// 取消合同
const onCancelVisitConfirm = async () => {
	try {
		const params = {
			id: cancelVisitModalConfig.value?.item.id,
			examineStatus: 2,
			isSendOneTimeCoupon: 0,
		};
		await jwCloudPedestal_V1_VisitorsReservation_Examine_POST(params);
		Taro.showToast({
			title: '预约已取消',
			icon: 'success',
		});
		// 刷新预约列表
		getVisitList();
	} catch (error) {
		console.error('取消合同失败:', error);
		Taro.showToast({
			title: error.message,
			icon: 'none',
		});
	} finally {
		cancelVisitModalConfig.value.isShow = false;
	}
};

const completeVisitModalConfig: any = ref({
	isShow: false,
	title: '完成预约',
	content: '是否发放出行优惠券？',
	confirmText: '是',
	cancelText: '否',
	item: null, // 用于存储当前操作的预约项
});

const onCompleteVisitConfirm = async (isGrantCoupon: boolean) => {
	try {
		const params = {
			id: completeVisitModalConfig.value?.item.id,
			examineStatus: 1,
			isSendOneTimeCoupon: isGrantCoupon ? 1 : 0,
		};
		await jwCloudPedestal_V1_VisitorsReservation_Examine_POST(params);
		Taro.showToast({
			title: '预约已完成',
			icon: 'success',
		});
		// 刷新合同列表
		getVisitList();
	} catch (error) {
		console.error('取消合同失败:', error);
		Taro.showToast({
			title: error.message,
			icon: 'none',
		});
	} finally {
		completeVisitModalConfig.value.isShow = false;
	}
};

const isRefreshing = ref(false);
// 下拉刷新处理
const onRefresh = async (isReloadPage: boolean = false) => {
	console.log('开始下拉刷新');
	currentPage.value = 1;
	isRefreshing.value = true;
	try {
		await new Promise((resolve) => setTimeout(resolve, 500));
		await onResetHandler();
		if (!isReloadPage) {
			Taro.showToast({
				title: '刷新成功',
				icon: 'success',
			});
		}
	} catch (error) {
		console.error('下拉刷新失败:', error);
		Taro.showToast({
			title: '刷新失败',
			icon: 'error',
		});
	} finally {
		isRefreshing.value = false;
		Taro.stopPullDownRefresh();
	}
};

// 使用 Taro 的下拉刷新 hook
Taro.usePullDownRefresh(onRefresh);
</script>
