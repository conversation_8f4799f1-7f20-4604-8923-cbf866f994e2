<template>
	<view class="profile">
		<view class="profile-body">
			<view class="profile-header">
				<view class="profile-avatar">
					<image :src="DefaultAvatar" class="avatar-img" mode="widthFix" />
				</view>
				<text class="profile-avatar-text">{{ userInfo?.trueName || '匿名用户' }}</text>
				<text class="profile-avatar-subtext" v-if="tagList.length > 0">
					{{ tagList.join('、') }}
				</text>
			</view>
			<view class="profile-info">
				<view class="profile-info-cell" @click="onResetPwdHandler">
					<view>重置密码</view>
					<RectRight />
				</view>
			</view>
			<!-- 保存修改 -->
			<nut-button
				type="danger"
				style="margin: 30px 20px; width: calc(100% - 40px); border-radius: 5px"
				@click="onLogoutHandler"
			>
				退出登录
			</nut-button>
		</view>
	</view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import './index.scss';
import { useUserStore } from '@/stores';
import { onLogoutHandler } from '@/utils/login';
import Taro from '@tarojs/taro';

// 引入图标组件
import { RectRight } from '@nutui/icons-vue-taro';
import DefaultAvatar from '@/assets/images/index_avatar.png';

const userStore = useUserStore();

const userInfo = computed(() => userStore.getUserInfo());

const tagList = ref<string[]>([]);

onMounted(() => {
	console.log('userInfo', userInfo.value);

	if (userInfo.value.admin) {
		tagList.value = ['超级管理员'];
	} else {
		if (userInfo.value.userRoles && userInfo.value.userRoles.length > 0) {
			tagList.value = userInfo.value.userRoles.map((item) => item?.roleName || '普通用户');
		} else {
			tagList.value = ['普通用户'];
		}
	}
});

const onResetPwdHandler = () => {
	Taro.navigateTo({
		url: '/pagesB/resetPwd/index',
	});
};
</script>
