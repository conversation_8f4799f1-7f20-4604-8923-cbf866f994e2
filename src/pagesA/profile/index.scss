.profile {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: #f5f5f5;
	.profile-body {
		background-color: #f5f5f5;
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 50px 0;
		.profile-header {
			display: flex;
			flex-direction: column;
			align-items: center;
			margin-bottom: 50px;
			gap: 5px;
			.profile-avatar {
				width: 180px;
				height: 180px;
				border-radius: 50%;
				overflow: hidden;
				flex-shrink: 0; // 防止头像被压缩
				.avatar-img {
					width: 100%;
					height: 100%;
				}
			}
			.profile-avatar-text {
				font-size: 30px;
				color: grey;
			}
			.profile-avatar-subtext {
				font-size: 28px;
				color: #4fc08d;
				text-align: center;
				padding: 0 30px;
			}
		}
		.profile-info {
			width: 100%;
			display: flex;
			align-items: center;
			flex-direction: column;
			padding: 0 30px;
			box-sizing: border-box;
			background-color: #fff;
			.profile-info-cell {
				width: 100%;
				border-bottom: 1px solid #f5f6f7;
				margin: 0 30px;
				padding: 20px;
				display: flex;
				align-items: center;
				justify-content: space-between;
				gap: 20px;
				font-size: 32px;
				color: #333;
			}
		}
	}
}
