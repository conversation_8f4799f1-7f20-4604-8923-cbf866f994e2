.house {
	min-height: 100vh;
	// background-color: #f5f6f7;
	display: flex;
	flex-direction: column;

	// 头部统计区域
	.house-header {
		// 以 #397fef 为主的蓝色渐变背景
		background: linear-gradient(
			135deg,
			#2968d4 0%,
			// 浅蓝色（左上）
			#397fef 35%,
			// 中浅蓝色
			#5ca0ff 60%,
			// 中蓝色
			#7fb8ff 95%,
			// 主色调蓝色
			#a8d5ff 100% // 深蓝色（右下）
		);
		padding: 30px;
		position: relative;

		// 选择区域
		.area-selector {
			display: flex;
			align-items: center;
			margin: 30px 0;

			.location-icon {
				margin-right: 10px;
			}

			.area-text {
				flex: 1;
				font-size: 30px;
				font-weight: 500;
				color: #fff;
				text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
			}
		}

		// 房态统计区域
		.stats-section {
			.stats-grid {
				display: grid;
				grid-template-columns: repeat(4, 1fr);
				gap: 0;

				.stat-item {
					display: flex;
					flex-direction: column;
					align-items: center;
					text-align: center;
					padding: 10px 5px;
					position: relative;

					.stat-label {
						font-size: 28px;
						color: rgba(255, 255, 255, 0.7);
						margin-bottom: 8px;
						font-weight: 500;
					}

					.stat-number {
						font-size: 32px;
						color: #fff;
						text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
						line-height: 1;
					}
					.stat-arrow {
						position: absolute;
						bottom: -30px;
						left: 50%;
						transform: translateX(-50%);
						width: 15px;
						height: 10px;
						background: white;
						clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
					}
				}
			}
		}
	}

	// 房态展示区域
	.house-content {
		flex: 1;
		padding: 30px;
		display: flex;
		flex-direction: column;

		.content-header {
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-bottom: 10px;

			.content-title {
				font-size: 32px;
				font-weight: bold;
				color: #333;
			}

			.legend {
				display: flex;
				align-items: center;
				gap: 20px;

				.legend-item {
					display: flex;
					align-items: center;
					gap: 8px;

					.legend-dot {
						width: 16px;
						height: 16px;
						border-radius: 50%;

						&.rented {
							background-color: #397fef;
						}

						&.vacant {
							background-color: #4caf50;
						}

						&.frozen {
							background-color: #999;
						}
					}

					.legend-text {
						font-size: 24px;
						color: #666;
					}
				}
			}
		}

		.house-list {
			flex: 1;
			position: relative;
			background-color: #fff;
			border-radius: 12px;
			box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
			display: flex;
			flex-direction: column;
			overflow-y: auto;
			.house-list-item {
				padding: 20px 0;
				border-bottom: 1px solid #f0f0f0;
				.floor-title {
					font-size: 32px;
					color: #333;
					display: flex;
					justify-content: space-between;
					align-items: center;
					padding: 0 30px;
					.toggle-button {
						.icon-wrapper {
							transition: transform 0.3s ease;
							display: flex;
							align-items: center;
							&.icon-rotated {
								transform: rotate(180deg);
							}
						}
					}
				}
				.floor-expandable-content {
					max-height: 0;
					overflow: hidden;
					transition: max-height 0.4s ease-in-out;
					width: 100%;
					&.expanded {
						max-height: 500px; // 足够大的值，确保内容完全显示
					}
					.content-wrapper {
						// 内容包装器，确保动画流畅
						padding: 20px 30px;
						display: grid;
						grid-template-columns: repeat(4, 1fr);
						gap: 20px;
						.house-item {
							display: flex;
							flex-direction: column;
							align-items: center;
							justify-content: center;
							padding: 20px;
							border-radius: 10px;
							box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
							color: #fff;
              font-size: 28px;

							&.rented {
								background-color: #397fef;
							}
							&.vacant {
								background-color: #4caf50;
							}
							&.frozen {
								background-color: #999;
							}
						}
					}
				}
			}

			.placeholder {
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -50%);
				width: 100%;
				text-align: center;
				font-size: 40px;
				color: #333;
			}
		}
	}
}
