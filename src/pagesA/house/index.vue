<template>
  <view class="house">
    <!-- 头部统计区域 -->
    <view class="house-header">
      <!-- 社区选择区域 -->
      <view class="area-selector" @click="onPeriodsPickerHandler">
        <Location2 class="location-icon" size="13" color="#fff" />
        <text class="area-text">{{ periodsVal || '请选择期数、幢数和单元' }}</text>
      </view>

      <!-- 房态统计区域 -->
      <view class="stats-section">
        <view class="stats-grid">
          <view class="stat-item" @click="onFormatHouseList(2)">
            <text class="stat-label">全部</text>
            <text class="stat-number">{{ houseStates.total }}</text>
            <view class="stat-arrow" v-if="selectedHouseState === 2"></view>
          </view>
          <view class="stat-item" @click="onFormatHouseList(1)">
            <text class="stat-label">已出租</text>
            <text class="stat-number">{{ houseStates.rented }}</text>
            <view class="stat-arrow" v-if="selectedHouseState === 1"></view>
          </view>
          <view class="stat-item" @click="onFormatHouseList(0)">
            <text class="stat-label">空闲</text>
            <text class="stat-number">{{ houseStates.vacant }}</text>
            <view class="stat-arrow" v-if="selectedHouseState === 0"></view>
          </view>
          <view class="stat-item" @click="onFormatHouseList(-1)">
            <text class="stat-label">冻结</text>
            <text class="stat-number">{{ houseStates.frozen }}</text>
            <view class="stat-arrow" v-if="selectedHouseState === -1"></view>
          </view>
        </view>
      </view>
    </view>

    <!-- 房态展示区域 -->
    <view class="house-content">
      <view class="content-header">
        <text class="content-title">房态展示</text>
        <view class="legend">
          <view class="legend-item">
            <view class="legend-dot rented"></view>
            <text class="legend-text">已出租</text>
          </view>
          <view class="legend-item">
            <view class="legend-dot vacant"></view>
            <text class="legend-text">空闲</text>
          </view>
          <view class="legend-item">
            <view class="legend-dot frozen"></view>
            <text class="legend-text">冻结</text>
          </view>
        </view>
      </view>

      <!-- 房屋列表区域 -->
      <view class="house-list">
        <view v-if="filterHouseList.length > 0">
          <view class="house-list-item" v-for="(item, index) in filterHouseList" :key="item.floor">
            <view class="floor-title" @click="onToggleClick(index)">
              <view>
                {{ item.floor }}层 {{ computedCurrentStateHouse(item) }}/{{ allHouseList[index].houseList.length }}
              </view>
              <view class="toggle-button">
                <view class="icon-wrapper" :class="{ 'icon-rotated': expandIndexList[index] }">
                  <TriangleDown size="10" color="#333" />
                </view>
              </view>
            </view>
            <!-- 可展开/收起的内容区域 -->
            <view class="floor-expandable-content"
              :class="{ expanded: expandIndexList[index] && item.houseList.length > 0 }">
              <view class="content-wrapper">
                <view class="house-item" v-for="house in item.houseList" :key="house.houseId"
                  :class="{ 'rented': house.houseStatus === 1, 'vacant': house.houseStatus === 0, 'frozen': house.houseStatus === -1 }"
                  @click="onHouseSelectHandler(house)">
                  <view class="house-name">{{ house.gatehouseNo }}室</view>
                </view>
              </view>
            </view>
          </view>
        </view>
        <view v-else>
          <text class="placeholder">请先选择期数、幢数和单元</text>
        </view>
      </view>
    </view>
  </view>
  <!-- NutUI Cascader 级联选择器 - 三级懒加载 -->
  <nut-cascader v-model:visible="visible" v-model="cascaderValue" title="期数、幢数和单元" lazy :lazy-load="cascaderLazyLoad"
    @change="onCascaderChange" @path-change="onPathChange" @close="onCascaderClose"></nut-cascader>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import './index.scss';
import { useAreaStore } from '@/stores';
import Taro from '@tarojs/taro';

import {
  jwCloudPedestal_V1_Room_AllTerms_GET,
  jwCloudPedestal_V1_Room_AllBuildingNos_GET,
  jwCloudPedestal_V1_Room_AllUnitNos_GET,
  jwCloudPedestal_V1_House_HouseByFloor_GET
} from '@/utils/api';

// 引入资源
import { Location2, TriangleDown } from '@nutui/icons-vue-taro';

// 使用 Pinia store
const areaStore = useAreaStore();
const currentArea = computed(() => areaStore.getCurrentArea());
const communityId = computed(() => currentArea.value?.areaId);

// 房态统计数据
const houseStates = ref({
  total: 0,
  rented: 0,
  vacant: 0,
  frozen: 0
});

// 房屋列表 - 所有房屋信息 - 按楼层分组
const allHouseList: any = ref([]);
// 房屋列表 - 过滤后的数据 - 按楼层分组
const filterHouseList: any = ref([]);

// 获取房屋信息列表(按楼层分组)
const getHouseList = async () => {
  const params = {
    communityId: communityId.value,
    term: cascaderValue.value[0], // 期数
    buildingNo: cascaderValue.value[1], // 幢数
    unit: cascaderValue.value[2], // 单元
  };
  const res = await jwCloudPedestal_V1_House_HouseByFloor_GET(params);
  allHouseList.value = res || [];
  filterHouseList.value = res || [];
  expandIndexList.value = new Array(res.length).fill(false);
  console.log('获取房屋信息列表(按楼层分组)', res);
  // 更新统计数据
  updateHouseStates();
};


// 更新房态统计数据
const updateHouseStates = () => {
  const allArr = allHouseList.value.map((item: any) => item.houseList).flat();
  houseStates.value = {
    total: allArr.length,
    rented: allArr.filter((item: any) => item.houseStatus === 1).length,
    vacant: allArr.filter((item: any) => item.houseStatus === 0).length,
    frozen: allArr.filter((item: any) => item.houseStatus === -1).length,
  };
};

// 选择的房屋状态
const selectedHouseState = ref(2);

const onFormatHouseList = (state: number) => {
  if (!periodsVal.value) {
    return Taro.showToast({
      title: '请先选择期数、幢数和单元',
      icon: 'none',
    });
  }
  if (selectedHouseState.value === state) {
    return;
  }
  selectedHouseState.value = state;
  if (state === 2) {
    filterHouseList.value = allHouseList.value;
  } else {
    filterHouseList.value = allHouseList.value.map((item: any) => ({
      ...item,
      houseList: item.houseList.filter((house: any) => house.houseStatus === state),
    }));
  }

  // console.log('filterHouseList', filterHouseList.value);
  // console.log('allHouseList', allHouseList.value);
}

// 计算当前状态的房屋数量
const computedCurrentStateHouse = (item: any) => {
  if (selectedHouseState.value === 2) {
    return item.houseList.filter((e: any) => e.houseStatus === 0).length;
  } else {
    return item.houseList.filter((e: any) => e.houseStatus === selectedHouseState.value).length;
  }
}

const expandIndexList: any = ref([]);
// 展开/收起点击事件
const onToggleClick = (index: number) => {
  expandIndexList.value[index] = !expandIndexList.value[index];
};

const onHouseSelectHandler = (house: any) => {
  console.log("house:", house);
  Taro.navigateTo({
    url: '/pagesB/house/houseDetail/index?houseId=' + house.houseId + '&houseStatus=' + house.houseStatus,
  })
}



// 期数/幢数/单元编号
const periodsVal = ref('');
// 期数/幢数/单元编号 picker Modal

// picker的点击事件
const onPeriodsPickerHandler = () => {
  visible.value = true;
};



// Cascader 级联选择器相关
const visible = ref(false);
const cascaderValue = ref<string[]>([]);

// 三级懒加载函数
const cascaderLazyLoad = async (node: any, resolve: any) => {
  console.log('懒加载节点:', node);
  try {
    // 第一级：加载期数（根级节点）
    if (node.root) {
      const terms = await jwCloudPedestal_V1_Room_AllTerms_GET(communityId.value);
      const termOptions = terms.map((term: string) => ({
        value: term,
        text: term + '期',
        leaf: false, // 不是叶子节点，还有下级
      }));

      resolve(termOptions);
      return;
    }

    // 第二级：加载幢数
    if (node.level === 0) {
      const buildings = await jwCloudPedestal_V1_Room_AllBuildingNos_GET(
        communityId.value,
        node.value, // 传入选中的期数
      );

      const buildingOptions = buildings.map((building: string) => ({
        value: building,
        text: building + '幢',
        leaf: false, // 不是叶子节点，还有下级
      }));

      resolve(buildingOptions);
      return;
    }

    // 第三级：加载单元号
    if (node.level === 1) {
      const units = await jwCloudPedestal_V1_Room_AllUnitNos_GET(
        communityId.value,
        node._parent?.value, // 期数
        node.value, // 幢数
      );

      const unitOptions = units.map((unit: string) => ({
        value: unit,
        text: unit + '单元',
        leaf: true, // 叶子节点，没有下级了
      }));

      resolve(unitOptions);
      return;
    }
  } catch (error) {
    console.error('懒加载数据失败:', error);
    resolve([]);
    // 显示错误提示
    Taro.showToast({
      title: '数据加载失败，请重试',
      icon: 'none',
    });
  }
};

// Cascader 选择完成事件
const onCascaderChange = (value: any, pathNodes: any) => {
  console.log('Cascader选择完成:', value, pathNodes);
  // 更新显示值
  if (pathNodes && pathNodes.length > 0) {
    const selectedTexts = pathNodes.map((node: any) => node.text);
    periodsVal.value = selectedTexts.join(' - ');
    console.log('更新显示值:', periodsVal.value);
    // 期数、幢数和单元选择完成，请求房间列表
    if (pathNodes.length === 3) {
      cascaderValue.value = value;
      getHouseList();
    }
  }

  // 关闭选择器
  visible.value = false;
};

// Cascader 路径变化事件
const onPathChange = (value: any, pathNodes: any) => {
  console.log('Cascader路径变化:', value, pathNodes);
};

// Cascader 关闭事件
const onCascaderClose = () => {
  console.log('Cascader关闭');
  visible.value = false;
};


// 刷新页面 - 提供给子页面调用
const reloadPage = () => {
	console.log('我需要重新加载数据！！！');
	getHouseList();
};

// 将方法暴露给外部调用（通过页面实例）
const currentInstance = Taro.getCurrentInstance();
if (currentInstance && currentInstance.page) {
	// 将刷新方法挂载到页面实例上，供其他页面调用
	(currentInstance.page as any).reloadPage = reloadPage;
}


</script>
