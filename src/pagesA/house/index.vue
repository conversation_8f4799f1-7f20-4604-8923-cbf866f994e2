<template>
  <view class="house"></view>
</template>

<script setup lang="ts">
import { onMounted, computed } from 'vue';
import './index.scss';
import { useAreaStore } from '@/stores';

import { jwCloudPedestal_V1_House_HouseByFloor_GET } from '@/utils/api';

// 使用 Pinia store
const areaStore = useAreaStore();
const currentArea = computed(() => areaStore.getCurrentArea());

onMounted(() => {
  getHouseList();
});

const getHouseList = async () => {
  const params = {
    communityId: '001',//TODO:currentArea.value?.areaId,
    term: '新乡数据',
    buildingNo: '1',
    unit: '修改2单元',
    houseStatus:'1'
  };
  const res = await jwCloudPedestal_V1_House_HouseByFloor_GET(params);
  console.log('获取房屋信息列表(按楼层分组)', res);
};
</script>
