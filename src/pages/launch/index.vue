<template>
	<view class="launch-page">
		<view class="launch-content">
			<view class="logo-container">
				<view class="logo-text">A</view>
			</view>
			<view class="app-name">A能家园社区管理</view>
			<view class="loading-container">
				<view class="loading-spinner"></view>
				<text class="loading-text">正在加载...</text>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
import { onMounted } from 'vue';
import './index.scss';
import Taro from '@tarojs/taro';
import { useUserStore } from '@/stores';

const userStore = useUserStore();

onMounted(() => {
	// 延迟显示启动页，然后检查登录状态并跳转
	setTimeout(() => {
		checkLoginAndRedirect();
	}, 2000); // 延迟2秒，你可以根据需要调整这个时间
});

const checkLoginAndRedirect = () => {
	try {
		const isLoggedIn = userStore.isLogin();

		console.log('启动页检查登录状态:', isLoggedIn);

		if (isLoggedIn) {
			// 已登录，跳转到首页
			Taro.switchTab({
				url: '/pages/index/index',
			});
		} else {
			// 未登录，跳转到登录页
			Taro.redirectTo({
				url: '/pages/login/index',
			});
		}
	} catch (error) {
		console.error('检查登录状态失败:', error);
		// 出错时跳转到登录页
		Taro.redirectTo({
			url: '/pages/login/index',
		});
	}
};
</script>
