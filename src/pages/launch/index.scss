.launch-page {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(
		135deg,
		#2968d4 0%,
		#397fef 35%,
		#5ca0ff 60%,
		#7fb8ff 95%,
		#a8d5ff 100%
	);
	display: flex;
	align-items: center;
	justify-content: center;
}

.launch-content {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	text-align: center;
}

.logo-container {
	margin-bottom: 40px;

	.logo-text {
		width: 120px;
		height: 120px;
		border-radius: 20px;
		background: rgba(255, 255, 255, 0.2);
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 60px;
		font-weight: bold;
		color: #fff;
		backdrop-filter: blur(10px);
		border: 2px solid rgba(255, 255, 255, 0.3);
	}
}

.app-name {
	font-size: 48px;
	font-weight: bold;
	color: #fff;
	margin-bottom: 80px;
	text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.loading-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 20px;
}

.loading-spinner {
	width: 40px;
	height: 40px;
	border: 4px solid rgba(255, 255, 255, 0.3);
	border-top: 4px solid #fff;
	border-radius: 50%;
	animation: spin 1s linear infinite;
}

.loading-text {
	color: #fff;
	font-size: 28px;
	opacity: 0.8;
}

@keyframes spin {
	0% {
		transform: rotate(0deg);
	}
	100% {
		transform: rotate(360deg);
	}
}
