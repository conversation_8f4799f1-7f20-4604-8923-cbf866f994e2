<template>
	<view class="index">
		<view class="gradient-header">
			<view class="custom-nav-bar" :style="{ height: `${getNavBarHeight()}px` }">
				<view
					class="center-title"
					:style="{ marginTop: `${getNavBarHeight() - getMenuHeight()}px` }"
				>
					个人中心
				</view>
			</view>
			<view class="user-info-content">
				<view class="user-role">
					<text class="user-role-text">你好，{{ userInfo?.trueName || '匿名用户' }}</text>
					<view class="user-role-tags">
						<nut-tag v-for="(item, index) in tagList" :key="index" color="#4fc08d">
							{{ item }}
						</nut-tag>
					</view>
				</view>
				<view class="user-avatar" @click="onAvatarClickHandler">
					<image
						class="avatar-img"
						src="@/assets/images/index_avatar.png"
						mode="widthFix"
					/>
				</view>
			</view>
			<view class="user-info-footer">
				<view class="footer-area" @click="onAreaPickerHandler">
					<TriangleDown color="#fff" size="15" />
					<text>{{ areaName }}</text>
				</view>
				<view class="footer-logout" @click="onLogoutHandler">
					<image src="@/assets/images/index_logout.png" alt="" mode="widthFix" />
				</view>
			</view>
		</view>
		<view class="index-body">
			<view
				class="index-body-cell"
				v-for="item in funcList"
				:key="item.key"
				@click="onFuncCellClickHandler(item)"
			>
				<view class="index-body-cell-left">
					<image :src="item.icon" alt="" mode="widthFix" />
					<text>{{ item.title }}</text>
				</view>
				<view class="index-body-cell-right">
					<text v-if="item.key === 'workOrder' && undoWorkOrderCount > 0">
						{{ undoWorkOrderCount }}
					</text>
					<RectRight color="#333" size="15" />
				</view>
			</view>
		</view>
		<!-- 区域选择 -->
		<NutPopup v-model:visible="showAreaList" position="bottom">
			<NutPicker
				:columns="areaList"
				title="请选择区域"
				@confirm="pickerConfirm"
				@cancel="showAreaList = false"
			/>
		</NutPopup>
	</view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import './index.scss';
import { getNavBarHeight, getMenuHeight } from '@/utils/screen';
import { useAreaStore, useUserStore, useWorkOrderStore } from '@/stores';
import { onLogoutHandler } from '@/utils/login';
import Taro from '@tarojs/taro';
import { getStore, saveStore } from '@/utils/utils';
import { isWorkOrderAdmin } from '@/utils/role';
import {
	jwCloudSystem_V1_Area_List_GET,
	jwCloudAuth_SecurityCenter_System_UserGroup_GetBindCommunity_GET,
	jwCloudMaintain_V1_Maintain_WorkOrder_Page_GET,
} from '@/utils/api';

// 引入图标组件
import { TriangleDown, RectRight } from '@nutui/icons-vue-taro';
import FuncRoomPNG from '@/assets/images/func_room.png';
import FuncTenantPNG from '@/assets/images/func_tenant.png';
import FuncContractPNG from '@/assets/images/func_contract.png';
import FuncBillPng from '@/assets/images/func_bill.png';
import FuncVisitPng from '@/assets/images/func_visit.png';
import FuncDelayReviewPng from '@/assets/images/func_delay_review.png';
import FuncWorkOrderPng from '@/assets/images/func_work_order.png';

const userStore = useUserStore();
const userInfo = computed(() => userStore.getUserInfo());

const tagList = ref<string[]>([]);

onMounted(() => {
	console.log('userInfo', userInfo.value);
	// 初始化当前区域
	const currentArea = getStore('currentArea');
	if (currentArea) {
		areaStore.setCurrentArea(currentArea);
	}

	if (userInfo.value?.userGroups && userInfo.value.userGroups.length > 0) {
		getAreaList(userInfo.value.userGroups[0].id);
	} else {
		getAreaList();
	}

	if (userInfo.value.admin) {
		tagList.value = ['超级管理员'];
	} else {
		if (userInfo.value.userRoles && userInfo.value.userRoles.length > 0) {
			tagList.value = userInfo.value.userRoles.map((item) => item?.roleName || '普通用户');
		} else {
			tagList.value = ['普通用户'];
		}
	}
});

const areaName = ref('选择区域');
const areaId = ref('');
const areaList = ref([]);
const showAreaList = ref(false);
const onAreaPickerHandler = () => {
	if (areaList.value.length === 0) {
		Taro.showToast({
			title: '当前没有可选区域',
			icon: 'none',
		});
		return;
	}
	showAreaList.value = true;
};

const pickerConfirm = ({ selectedOptions, selectedValue }: any) => {
	areaId.value = selectedValue[0];
	areaName.value = selectedOptions[0].text;
	showAreaList.value = false;
	// 保存当前选择的园区
	const selectedArea: any = treeNodeInfoAndAreaList.value.find(
		(item: any) => item.areaId === selectedValue[0],
	);
	if (selectedArea && selectedArea.areaId !== currentArea.value.areaId) {
		saveStore('currentArea', selectedArea);
		areaStore.setCurrentArea(selectedArea);
	}
};

const areaStore = useAreaStore();
const currentArea = computed(() => areaStore.getCurrentArea());
// 后端接口获取的园区列表
let treeNodeInfoAndAreaList = ref([]);
// 获取园区信息
const getAreaList = async (groupId: string | null = null) => {
	treeNodeInfoAndAreaList.value = [];
	try {
		let res: any = [];
		if (groupId) {
			res = await jwCloudAuth_SecurityCenter_System_UserGroup_GetBindCommunity_GET(groupId);
		} else {
			res = await jwCloudSystem_V1_Area_List_GET();
		}
		if (res && res.length > 0) {
			treeNodeInfoAndAreaList.value = res;
			areaList.value = res.map((item: any) => ({
				text: item.areaName,
				value: item.areaId,
			}));
			if (currentArea.value) {
				// 如果当前有选择的园区，则设置为默认值
				areaId.value = currentArea.value.areaId;
				areaName.value = currentArea.value.areaName;
			} else {
				// 如果没有选择的园区，则设置为第一个
				areaId.value = res[0].areaId;
				areaName.value = res[0].areaName;
				saveStore('currentArea', res[0]);
				areaStore.setCurrentArea(res[0]);
			}
			// 获取到园区之后开始查询工单个数
			getWorkOrderList();
		}

		// 检查返回数据是否为空
		if (!res || (Array.isArray(res) && res.length === 0)) {
			console.warn('获取园区信息为空，可能需要先配置园区数据');
			return;
		}
	} catch (error) {
		console.error('获取园区信息失败:', error);
		// 根据错误类型给出不同的提示
		if (error.message && error.message.includes('IndexOutOfBoundsException')) {
			console.warn('后端数据为空，请检查园区配置');
			// 可以显示一个友好的提示给用户
			Taro.showToast({
				title: '当前没有可选区域',
				icon: 'none',
			});
		}
	}
};

const onAvatarClickHandler = () => {
	Taro.navigateTo({
		url: '/pagesA/profile/index',
	});
};

const funcList: any[] = [
	{
		title: '房间管理',
		icon: FuncRoomPNG,
		path: '/pagesA/room/index',
		key: 'room',
	},
	{
		title: '房客管理',
		icon: FuncTenantPNG,
		path: '/pagesA/tenant/index',
		key: 'tenant',
	},
	{
		title: '合同审核',
		icon: FuncContractPNG,
		path: '/pagesA/contract/index',
		key: 'contract',
	},
	{
		title: '租户签约',
		icon: FuncContractPNG,
		path: '/pagesA/contractSign/index',
		key: 'contractSign',
	},
	{
		title: '房租账单',
		icon: FuncBillPng,
		path: '/pagesA/bill/index',
		key: 'bill',
	},
	{
		title: '来访管理',
		icon: FuncVisitPng,
		path: '/pagesA/visit/index',
		key: 'visit',
	},
	{
		title: '延期审核',
		icon: FuncDelayReviewPng,
		path: '/pagesA/delayReview/index',
		key: 'delayReview',
	},
	{
		title: '报修管理',
		icon: FuncWorkOrderPng,
		path: isWorkOrderAdmin()
			? '/pagesA/assignWorkOrder/index'
			: '/pagesB/workOrder/todoWorkOrder/index',
		key: 'workOrder',
	},
];

const onFuncCellClickHandler = (item: any) => {
	if (!currentArea.value) {
		Taro.showToast({
			title: '请先选择区域',
			icon: 'none',
		});
		return;
	}

	Taro.navigateTo({
		url: item.path,
	}).catch((error) => {
		console.error('导航失败:', error);

		// 判断错误类型，提供更精确的提示
		let errorMessage = '当前模块未配置';

		if (error.errMsg) {
			if (
				error.errMsg.includes('page not found') ||
				error.errMsg.includes('页面不存在') ||
				error.errMsg.includes('can not find page')
			) {
				errorMessage = '页面不存在，模块未配置';
			} else if (error.errMsg.includes('navigateTo fail')) {
				errorMessage = '页面跳转失败';
			}
		}

		Taro.showToast({
			title: errorMessage,
			icon: 'none',
			duration: 2000,
		});
	});
};

// 动态监听剩余的工单数
watch(
	() => useWorkOrderStore().getUndoWorkOrderCount(),
	(newVal) => {
		console.log('监听工单数触发了么？', newVal);
		undoWorkOrderCount.value = newVal;
	},
);

const undoWorkOrderCount = ref(0);
const getWorkOrderList = async () => {
	try {
		const params = {
			pageNum: 1,
			pageSize: 999,
			areaId: currentArea.value.areaId,
			state: isWorkOrderAdmin() ? '0,1' : '1',
			currentUserWorkOrder: !isWorkOrderAdmin(),
		};
		const res = await jwCloudMaintain_V1_Maintain_WorkOrder_Page_GET(params);
		undoWorkOrderCount.value = res.items.length;
		useWorkOrderStore().setUndoWorkOrderCount(res.items.length);
	} catch (error) {
		console.error('获取工单列表失败:', error);
	}
};
</script>
