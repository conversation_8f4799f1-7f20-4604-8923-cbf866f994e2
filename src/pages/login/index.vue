<template>
	<view class="login-container">
		<view class="login-form">
			<view class="form-item">
				<NutInput v-model="loginForm.username" placeholder="请输入登录名">
					<template #left>
						<image src="@/assets/images/login_account.png" alt="" mode="aspectFill" />
					</template>
				</NutInput>
			</view>
			<view class="form-item">
				<NutInput v-model="loginForm.password" type="password" placeholder="请输入登录密码">
					<template #left>
						<image src="@/assets/images/login_pwd.png" alt="" mode="aspectFill" />
					</template>
				</NutInput>
			</view>
			<view class="login-btn-container">
				<NutButton
					type="primary"
					size="large"
					:loading="isLoading"
					@click="handleLogin"
					class="login-btn"
				>
					登录
				</NutButton>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import Taro from '@tarojs/taro';
import CryptoJS from 'crypto-js';
import './index.scss';
import { onLoginHandler } from '@/utils/login';
// 登录表单数据
const loginForm = reactive({
	username: '',
	password: '',
});

const isLoading = ref(false);

// 模拟登录处理函数
const handleLogin = async () => {
	// 基本验证
	if (!loginForm.username.trim()) {
		Taro.showToast({
			title: '请输入登录名',
			icon: 'none',
		});
		return;
	}

	if (!loginForm.password.trim()) {
		Taro.showToast({
			title: '请输入登录密码',
			icon: 'none',
		});
		return;
	}
	try {
		isLoading.value = true;
		// 等待500ms
		await new Promise((resolve) => setTimeout(resolve, 500));
		// 对密码进行MD5加密
		const encryptedPassword = CryptoJS.MD5(loginForm.password).toString();
		await onLoginHandler(loginForm.username, encryptedPassword);
	} catch (error) {
		console.error('登录失败:', error);
		Taro.showToast({
			title: '登录失败，请重试',
			icon: 'none',
		});
	} finally {
		isLoading.value = false;
	}
};
</script>
