.message {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: #f5f5f5;
	.body-card {
		display: flex;
		align-items: center;
		flex-direction: column;
		margin-top: 30px; // 可调整
		padding: 0 30px;
		background-color: #fff;
		.body-card-cell {
			width: 100%;
			border-bottom: 1px solid #f0f0f0;
			padding: 30px;
			display: flex;
			align-items: center;
			justify-content: space-between;
			font-size: 32px;
			font-weight: bold;
			color: #333;
			.body-card-cell-left {
				display: flex;
				align-items: center;
				gap: 20px;
				.body-card-cell-left-img {
					width: 54px;
					height: 54px;
				}
			}
			.body-card-cell-right {
				display: flex;
				align-items: center;
				gap: 15px;
				color: grey;
				font-size: 24px;
			}
		}
	}
}
