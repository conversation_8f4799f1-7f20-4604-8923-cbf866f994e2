import Taro from '@tarojs/taro';

/**
 * 图片下载工具类
 */
export class ImageDownloadUtil {
	/**
	 * 检查相册权限
	 * @returns Promise<boolean> 是否有权限
	 */
	static async checkAlbumAuth(): Promise<boolean> {
		try {
			const authSetting = await Taro.getSetting();
			if (authSetting.authSetting['scope.writePhotosAlbum'] === false) {
				// 用户之前拒绝了权限，需要引导到设置页面
				const modalResult = await Taro.showModal({
					title: '提示',
					content: '需要您授权保存图片到相册，请在设置中开启相册权限',
					confirmText: '去设置',
					cancelText: '取消',
				});

				if (modalResult.confirm) {
					await Taro.openSetting();
					// 重新检查权限
					const newAuthSetting = await Taro.getSetting();
					return newAuthSetting.authSetting['scope.writePhotosAlbum'] === true;
				}
				return false;
			}
			return true;
		} catch (error) {
			console.error('检查权限失败:', error);
			return false;
		}
	}

	/**
	 * 保存单张图片到相册
	 * @param imageUrl 图片URL
	 * @param showLoading 是否显示加载提示
	 * @returns Promise<boolean> 是否保存成功
	 */
	static async saveImageToAlbum(imageUrl: string, showLoading: boolean = true): Promise<boolean> {
		try {
			// 先检查权限
			const hasAuth = await this.checkAlbumAuth();
			if (!hasAuth) {
				return false;
			}

			// 显示加载提示
			if (showLoading) {
				Taro.showLoading({
					title: '保存中...',
					mask: true,
				});
			}

			// 先下载图片到本地临时路径
			const downloadResult = await Taro.downloadFile({
				url: imageUrl,
			});

			if (downloadResult.statusCode === 200) {
				// 保存图片到相册
				await Taro.saveImageToPhotosAlbum({
					filePath: downloadResult.tempFilePath,
				});

				if (showLoading) {
					Taro.hideLoading();
					Taro.showToast({
						title: '保存成功',
						icon: 'success',
						duration: 2000,
					});
				}

				return true;
			} else {
				throw new Error('图片下载失败');
			}
		} catch (error: any) {
			if (showLoading) {
				Taro.hideLoading();
			}

			console.error('保存图片失败:', error);

			if (error.errMsg && error.errMsg.includes('auth deny')) {
				// 权限被拒绝，引导用户开启权限
				Taro.showModal({
					title: '提示',
					content: '需要您授权保存图片到相册，请在设置中开启相册权限',
					confirmText: '去设置',
					success: (res) => {
						if (res.confirm) {
							Taro.openSetting();
						}
					},
				});
			} else {
				if (showLoading) {
					Taro.showToast({
						title: '保存失败',
						icon: 'error',
						duration: 2000,
					});
				}
			}

			return false;
		}
	}

	/**
	 * 批量保存图片到相册
	 * @param imageUrls 图片URL数组
	 * @param showProgress 是否显示进度
	 * @returns Promise<{successCount: number, failCount: number}> 保存结果统计
	 */
	static async saveMultipleImagesToAlbum(
		imageUrls: string[],
		showProgress: boolean = true,
	): Promise<{ successCount: number; failCount: number }> {
		if (imageUrls.length === 0) {
			Taro.showToast({
				title: '没有图片可保存',
				icon: 'none',
			});
			return { successCount: 0, failCount: 0 };
		}

		try {
			// 先检查权限
			const hasAuth = await this.checkAlbumAuth();
			if (!hasAuth) {
				return { successCount: 0, failCount: imageUrls.length };
			}

			if (showProgress) {
				Taro.showLoading({
					title: `保存中 0/${imageUrls.length}`,
					mask: true,
				});
			}

			let successCount = 0;
			let failCount = 0;

			for (let i = 0; i < imageUrls.length; i++) {
				try {
					const imageUrl = imageUrls[i];

					// 更新进度
					if (showProgress) {
						Taro.showLoading({
							title: `保存中 ${i + 1}/${imageUrls.length}`,
							mask: true,
						});
					}

					// 保存单张图片（不显示单独的loading）
					const success = await this.saveImageToAlbum(imageUrl, false);
					if (success) {
						successCount++;
					} else {
						failCount++;
					}
				} catch (error) {
					console.error(`保存第${i + 1}张图片失败:`, error);
					failCount++;
				}
			}

			if (showProgress) {
				Taro.hideLoading();

				if (failCount === 0) {
					Taro.showToast({
						title: `全部保存成功(${successCount}张)`,
						icon: 'success',
						duration: 2000,
					});
				} else {
					Taro.showToast({
						title: `保存完成: 成功${successCount}张, 失败${failCount}张`,
						icon: 'none',
						duration: 3000,
					});
				}
			}

			return { successCount, failCount };
		} catch (error) {
			if (showProgress) {
				Taro.hideLoading();
			}
			console.error('批量保存失败:', error);
			if (showProgress) {
				Taro.showToast({
					title: '批量保存失败',
					icon: 'error',
				});
			}
			return { successCount: 0, failCount: imageUrls.length };
		}
	}

	/**
	 * 显示图片操作菜单
	 * @param imageUrl 图片URL
	 * @param options 配置选项
	 */
	static showImageActionSheet(
		imageUrl: string,
		options: {
			enableDownload?: boolean;
			enablePreview?: boolean;
			onPreview?: () => void;
			onDownloadSuccess?: () => void;
			onDownloadError?: (error: any) => void;
		} = {},
	) {
		const {
			enableDownload = true,
			enablePreview = true,
			onPreview,
			onDownloadSuccess,
			onDownloadError,
		} = options;

		const itemList: any = [];
		if (enableDownload) itemList.push('保存到相册');
		if (enablePreview) itemList.push('预览图片');

		if (itemList.length === 0) return;

		Taro.showActionSheet({
			itemList,
			success: async (res) => {
				if (res.tapIndex === 0 && enableDownload) {
					// 保存到相册
					const success = await this.saveImageToAlbum(imageUrl);
					if (success && onDownloadSuccess) {
						onDownloadSuccess();
					} else if (!success && onDownloadError) {
						onDownloadError(new Error('保存失败'));
					}
				} else if (
					(res.tapIndex === 1 && enablePreview) ||
					(res.tapIndex === 0 && !enableDownload && enablePreview)
				) {
					// 预览图片
					if (onPreview) {
						onPreview();
					}
				}
			},
			fail: (err) => {
				console.log('操作取消:', err);
			},
		});
	}
}

// 导出便捷方法
export const saveImageToAlbum = ImageDownloadUtil.saveImageToAlbum.bind(ImageDownloadUtil);
export const saveMultipleImagesToAlbum =
	ImageDownloadUtil.saveMultipleImagesToAlbum.bind(ImageDownloadUtil);
export const showImageActionSheet = ImageDownloadUtil.showImageActionSheet.bind(ImageDownloadUtil);
export const checkAlbumAuth = ImageDownloadUtil.checkAlbumAuth.bind(ImageDownloadUtil);
