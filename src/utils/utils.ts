import {
	clearStorageSync,
	getStorageSync,
	reLaunch,
	removeStorageSync,
	setStorageSync,
} from '@tarojs/taro';
import { useAuthStore } from '@/stores';
import Taro from '@tarojs/taro';
import { getApiConfig } from '@/config/api';
/**
 *
 * @param {string} key
 * @param {string} data
 * @returns {void}
 */
export const saveStore = (key, data) => {
	return setStorageSync(key, data);
};
/**
 *
 * @param {string} key
 * @returns {void}
 */
export const removeStore = (key) => {
	return removeStorageSync(key);
};
/**
 *
 * @param {string} key
 * @returns {string}
 */
export const getStore = (key) => {
	return getStorageSync(key);
};
/**
 *
 * @param {string} key
 * @returns {void}
 */
export const clearStore = () => {
	return clearStorageSync();
};

/**
 *
 * @param {string} message 需要发送的消息
 * @param {string} key key,可以为空,如果含有key则尝试进行结构化.如果原先为字符串,因为多次结构化请注意数据格式
 */
export const postNativeMessage = (message: string, key = '') => {
	const native: any = window;
	let sendMessage = '';
	if (!key) {
		sendMessage = message;
	} else {
		sendMessage = JSON.stringify({ [key]: message });
	}
	native.postMessage(sendMessage);
};

export const checkUser = () => {
	//针对不存在userInfo的情况下,跳转到登陆页面
	const userInfo = getStore('userInfo');
	if (!userInfo) {
		//不存在,进行跳转
		reLaunch({ url: '/pages/login/index' });
		return false;
		// postNativeMessage('鉴权信息丢失,跳转到登录页', 'errorInfo');
	}
	return true;
};

export const userLogout = () => {
	const authStore = useAuthStore();
	authStore.$reset(); //重置存在pinia的信息
	clearStore(); //清理用户信息
	reLaunch({ url: '/pages/login/index' });
};

let timer;
/**
 * 特殊防抖，采用节流方式先执行，然后禁止durationTime秒
 * @param fn
 * @param durationTime
 * @returns
 */
export const debounce = (fn, durationTime = 300) => {
	if (timer) return;
	fn.call();
	timer = setTimeout(() => {
		clearTimeout(timer);
		timer = null;
	}, durationTime);
};
// 区分iOS、Android还是Browser
export const getDeviceInfo = () => {
	let userAgent = window.navigator.userAgent;
	var isAndroid = userAgent.indexOf('Android') > -1 || userAgent.indexOf('Adr') > -1; // Android终端
	var isiOS = !!userAgent.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); // iOS终端
	if (isAndroid) {
		return 'Android';
	} else if (isiOS) {
		return 'iOS';
	} else {
		return 'Browser';
	}
};

// 检查Token
export const checkedToken = () => {
	const authStore = useAuthStore();
	if (authStore.token) {
		return true;
	} else {
		return false;
	}
};

// 缺token导致的退出登录
export const missingTokenandLogout = () => {
	// token不存在退出登录 - 第一次获取token的方法会塞入一个tempToken以示区分
	saveStore('logout', true);
	reLaunch({ url: '/pages/login' });
};

// 图片base64转换
export const convertToBase64 = (filePath) => {
	const fs = Taro.getFileSystemManager();
	return new Promise((resolve, reject) => {
		fs.readFile({
			filePath,
			encoding: 'base64',
			success(res) {
				const base64 = 'data:image/jpeg;base64,' + res.data;
				resolve(base64);
			},
			fail(err) {
				reject(err);
			},
		});
	});
};

// 获取完整的图片地址
export const getFullAvatarUrl = (url: string) => {
	if (!url) {
		return '';
	}
	if (url.startsWith('http')) {
		return url;
	}
	return `${getApiConfig().baseURL}${url}`;
};

// 日期格式化，默认格式 YYYY-MM-DD HH:mm:ss，支持自定义格式
export const formatDate = (date: Date, format: string = 'YYYY-MM-DD HH:mm:ss') => {
	const year = date.getFullYear();
	const month = String(date.getMonth() + 1).padStart(2, '0');
	const day = String(date.getDate()).padStart(2, '0');
	const hour = String(date.getHours()).padStart(2, '0');
	const minute = String(date.getMinutes()).padStart(2, '0');
	const second = String(date.getSeconds()).padStart(2, '0');

	format = format.replace('YYYY', year.toString());
	format = format.replace('MM', month);
	format = format.replace('DD', day);
	format = format.replace('HH', hour);
	format = format.replace('mm', minute);
	format = format.replace('ss', second);

	return format;
};

// 格式化时间转成Date对象，默认格式 YYYY-MM-DD HH:mm:ss
export const formatTimeToDate = (time: string, format: string = 'YYYY-MM-DD HH:mm:ss') => {
	return new Date(formatDate(new Date(time), format));
};

// 距离当前的时间——时间格式化
export const timeLineFormat = (date: Date) => {
	const now = new Date();
	const diff = now.getTime() - date.getTime();
	const diffDay = Math.floor(diff / (1000 * 60 * 60 * 24));
	const diffHour = Math.floor(diff / (1000 * 60 * 60));
	const diffMinute = Math.floor(diff / (1000 * 60));
	if (diffHour < 1) {
		return `${diffMinute}分钟前`;
	} else if (diffDay < 1) {
		return `${diffHour}小时前`;
	} else if (diffDay < 7) {
		return `${diffDay}天前`;
	} else if (diffDay < 365) {
		return formatDate(date, 'MM-DD');
	} else {
		return formatDate(date, 'YYYY-MM-DD');
	}
};

// 数字转中文
export const numberToChinese = (num: number): string => {
	if (num < 1 || num > 99) {
		return num.toString();
	}
	const digits = ['', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
	if (num < 10) {
		return digits[num];
	}
	if (num === 10) {
		return '十';
	}
	if (num < 20) {
		return '十' + digits[num % 10];
	}
	const tens = Math.floor(num / 10);
	const ones = num % 10;
	if (ones === 0) {
		return digits[tens] + '十';
	}
	return digits[tens] + '十' + digits[ones];
};

// 金额格式化
export const formatMoneyWithCommas = (value: number | string, decimal?: number): string => {
	// 尝试将值转换为数字
	const num = Number(value);
	// 检查转换后的值是否是有效数字
	if (isNaN(num)) {
		// 正则表达式：非数字校验校验是否匹配带千分位的数值格式
		const regex = /^\d{1,3}(,\d{3})*(\.\d+)?$/;
		if (regex.test(value.toString())) {
			return value.toString();
		}
		return value.toString(); // 或者可以返回原值 value
	}
	// 转换为千分位格式
	return num
		.toFixed(decimal || 0)
		.toString()
		.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

// 预览文件
export const previewFile = async (url) => {
	// 文件类型数组
	const fileTypeList = ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'pdf'];
	// 构建正则表达式模式
	const regexPattern = fileTypeList.map((type) => `\\.${type}(?=[?#]|$)`).join('|');
	const fileTypeRegex = new RegExp(regexPattern, 'i'); // 'i' 表示不区分大小写
	// 从 URL 中提取文件类型
	const match = url.match(fileTypeRegex);
	if (!match) {
		Taro.showToast({
			title: '该文件不支持预览',
			icon: 'none',
		});
		return;
	}
	// 文件类型并转成小写
	const fileType = match[0].slice(1).toLowerCase(); // 移除点号，并转成小写
	const fileUrl = url.startsWith('http') ? url : `${getApiConfig().baseURL}${url}`;
	try {
		// 1. 下载文件到本地临时路径
		const downloadTask = Taro.downloadFile({
			url: fileUrl,
			success: (res) => {
				if (res.statusCode === 200) {
					const tempFilePath = res.tempFilePath;

					// 2. 根据平台选择预览方式
					if (process.env.TARO_ENV === 'weapp') {
						// 微信小程序
						Taro.openDocument({
							filePath: tempFilePath,
							fileType: fileType,
							success: () => console.log('文件打开成功'),
							fail: (err) => console.error('文件打开失败', err),
						});
					} else {
						// H5 或其他平台
						window.open(fileUrl, '_blank');
					}
				}
			},
			fail: (err) => {
				console.error('下载文件失败', err);
				Taro.showToast({
					title: '文件下载失败，请重试',
					icon: 'none',
				});
			},
		});

		// 下载进度监听
		downloadTask.onProgressUpdate((res) => {
			// console.log('下载进度', res.progress);
			// console.log('已经下载的数据长度', res.totalBytesWritten);
			// console.log('预期需要下载的数据长度', res.totalBytesExpectedToWrite);
		});
	} catch (error) {
		console.error('预览文件出错', error);
	}
};

// 获取本周的开始时间
export const getWeekStart = () => {
  const date = new Date();
  const day = date.getDay();
  const diff = date.getDate() - day + (day === 0 ? -6 : 1);
  return formatDate(new Date(date.setDate(diff)), 'YYYY-MM-DD');
};

// 获取本周的结束时间
export const getWeekEnd = () => {
  const date = new Date();
  const day = date.getDay();
  const diff = date.getDate() - day + (day === 0 ? 0 : 7);
  return formatDate(new Date(date.setDate(diff)), 'YYYY-MM-DD');
};

// 获取本月的开始时间
export const getMonthStart = () => {
  const date = new Date();
  return formatDate(new Date(date.getFullYear(), date.getMonth(), 1), 'YYYY-MM-DD');
};

// 获取本月的结束时间
export const getMonthEnd = () => {
  const date = new Date();
  return formatDate(new Date(date.getFullYear(), date.getMonth() + 1, 0), 'YYYY-MM-DD');
};

// 获取本季度的开始时间
export const getQuarterStart = () => {
  const date = new Date();
  const month = date.getMonth();
  const quarter = Math.floor(month / 3) * 3;
  return formatDate(new Date(date.getFullYear(), quarter, 1), 'YYYY-MM-DD');
};

// 获取本季度的结束时间
export const getQuarterEnd = () => {
  const date = new Date();
  const month = date.getMonth();
  const quarter = Math.floor(month / 3) * 3 + 2;
  return formatDate(new Date(date.getFullYear(), quarter + 1, 0), 'YYYY-MM-DD');
};


// 如何将时间字符YYYY-MM-DD HH:mm:ss 转换为时间戳
export const dateTimeToTimestamp = (dateTime: string): number => {
  const date = new Date(dateTime.replace(/-/g, '/'));
  return date.getTime();
};

