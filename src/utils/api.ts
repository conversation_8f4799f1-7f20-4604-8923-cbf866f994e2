import http from '@/libs/request';
import Taro from '@tarojs/taro';
import { getApiConfig } from '@/config/api';

// 登录
export const jwCloudAuth_SecurityCenter_Login_POST = (params) =>
	http.post(`/jw-cloud-auth/security-center/login`, params, {
		header: {
			'Content-Type': 'application/json;charset=UTF-8',
		},
		needAuth: false,
	});

// 获取用户信息
export const jwCloudAuth_SecurityCenter_System_User_GetWebCurrentUserInfo_GET = () => {
	return http.get(`/jw-cloud-auth/security-center/system/user/getWebCurrentUserInfo`);
};

// 退出登录
export const jwCloudAuth_SecurityCenter_Quit_POST = () => {
	return http.post(`/jw-cloud-auth/security-center/quit`);
};

// 修改用户信息
export const jwCloudAuth_SecurityCenter_System_User_AppUserEdit_POST = (body) => {
	return http.post(`/jw-cloud-auth/security-center/system/user/appUserEdit`, body, {
		header: {
			'Content-Type': 'application/x-www-form-urlencoded',
		},
	});
};

// 重置密码
export const jwCloudAuth_SecurityCenter_SetpasswdNew_POST = (oldPsd: string, newPsd: string) => {
	return http.post(
		`/jw-cloud-auth/security-center/setpasswdNew`,
		{
			oldPsd,
			newPsd,
		},
		{
			header: {
				'Content-Type': 'application/x-www-form-urlencoded',
			},
		},
	);
};

/**
 * 上传文件 - 使用 FormData 方式
 * @param file 文件对象或文件路径
 * @param path 可选的文件路径参数
 */
export const jwCloudPedestal_V1_Fastdfs_UploadFile_POST = (file: string | File, path?: string) => {
	return new Promise((resolve, reject) => {
		// 获取认证 token
		import('@/stores').then(({ useAuthStore }) => {
			const authStore = useAuthStore();

			// 使用 Taro.uploadFile 进行文件上传
			Taro.uploadFile({
				url: `${getApiConfig().baseURL}/jw-cloud-pedestal/v1/fastdfs/uploadFile`,
				filePath: typeof file === 'string' ? file : '', // 如果是文件路径
				name: 'file', // 后端接收的字段名
				formData: {
					// 可选的 path 参数
					...(path && { path }),
				},
				header: {
					// 添加认证头部
					accessToken: authStore.token || '',
					// 不需要手动设置 Content-Type，Taro 会自动设置为 multipart/form-data
				},
				success: (res) => {
					try {
						const data = JSON.parse(res.data);
						if (data.ret === 0 || data.info === 'success') {
							resolve(data.body || data);
						} else {
							reject(new Error(data.message || '上传失败'));
						}
					} catch (error) {
						reject(new Error('上传失败'));
					}
				},
				fail: (error) => {
					reject(error);
				},
			});
		});
	});
};

/**
 * 上传 base64 图片文件
 * @param base64Data base64 图片数据
 * @param path 可选的文件路径参数
 */
// export const jwCloudPedestal_V1_Fastdfs_UploadBase64Image_POST = (base64Data: string, path?: string) => {
// 	const body = {
// 		base64Data,
// 		// 只有 path 不为空时才添加到 body 中
// 		...(path && path !== '' && { path }),
// 	};

// 	return http.post(
// 		`/jw-cloud-pedestal/v1/fastdfs/uploadBase64`,
// 		body,
// 		{
// 			header: {
// 				'Content-Type': 'application/json',
// 			},
// 		}
// 	);
// };

/**
 * 通过 URL 下载并上传文件
 * @param imageUrl 图片 URL（如微信头像 URL）
 * @param path 可选的文件路径参数
 */
export const downloadAndUploadImage = async (imageUrl: string, path?: string) => {
	try {
		// 第一步：下载图片到本地临时文件
		const downloadResult = await new Promise<string>((resolve, reject) => {
			Taro.downloadFile({
				url: imageUrl,
				success: (res) => {
					if (res.statusCode === 200) {
						resolve(res.tempFilePath);
					} else {
						reject(new Error(`下载失败，状态码：${res.statusCode}`));
					}
				},
				fail: (error) => {
					reject(new Error(`下载失败：${error.errMsg}`));
				},
			});
		});

		// 第二步：上传下载的临时文件
		const uploadResult = await jwCloudPedestal_V1_Fastdfs_UploadFile_POST(downloadResult, path);
		return uploadResult;
	} catch (error) {
		console.error('下载并上传图片失败:', error);
		throw error;
	}
};

/**
 * 微信头像上传的便捷方法
 * @param avatarUrl 微信头像 URL
 */
export const uploadWechatAvatar = async (avatarUrl: string) => {
	return downloadAndUploadImage(avatarUrl, 'avatars/');
};

/**
 * 获取园区信息
 */
export const jwCloudSystem_V1_Area_List_GET = () => {
	return http.get(`/jw-cloud-system/v1/area/list`);
};

/**
 * 根据groupId获取园区信息
 * @param groupId 园区id
 */
export const jwCloudAuth_SecurityCenter_System_UserGroup_GetBindCommunity_GET = (groupId: string) =>
	http.get(
		`/jw-cloud-auth/security-center/system/userGroup/getBindCommunity`,
		{
			groupId,
		},
		{
			header: {
				'Content-Type': 'application/x-www-form-urlencoded',
			},
		},
	);

/**
 * 获取预约看房列表
 * @param params
 * pageNum 页码
 * pageSize 每页数量
 */
export const jwCloudPedestal_V1_VisitorsReservation_Page_GET = (params) =>
	http.get(`/jw-app-api/v1/visitorsReservation/page`, params, {
		header: {
			'Content-Type': 'application/x-www-form-urlencoded',
		},
	});

/**
 * 预约处理
 * @param params
 * id 预约id
 * examineStatus 处理状态 1：同意 2：拒绝
 * isSendOneTimeCoupon 是否发放优惠券 1：是 0：否
 */
export const jwCloudPedestal_V1_VisitorsReservation_Examine_POST = (params) =>
	http.post(`/jw-app-api/v1/visitorsReservation/examine`, params, {
		header: {
			'Content-Type': 'application/json',
		},
	});

/**
 * 根据字典类型获取字典列表  工单类型 【ims，orderType】
 * @param params
 * systemCode 系统编码
 * groupCode 字典分组编码
 */
export const jwCloudSystem_V1_Dictionary_ListWebSelection_GET = (params) =>
	http.get(`/jw-cloud-system/v1/dictionary/listWebSelection`, params, {
		header: {
			'Content-Type': 'application/x-www-form-urlencoded',
		},
	});

/**
 * 工单列表
 * @param params
 * pageNum 页码
 * pageSize 每页数量
 * workOrderType 工单类型
 * workOrderName 工单名称
 * reportBeginTime 开始时间
 * reportEndTime 结束时间
 * areaId 区域id
 * workOrderNum 工单编号
 */
export const jwCloudMaintain_V1_Maintain_WorkOrder_Page_GET = (params) =>
	http.get(`/jw-cloud-maintain/v1/maintain/WorkOrder/page`, params, {
		header: {
			'Content-Type': 'application/x-www-form-urlencoded',
		},
	});

/**
 * 工单详情历史记录
 * @param workOrderNum 工单编号
 */
export const jwCloudMaintain_V1_Maintain_WorkOrderLife_Query_GET = (workOrderNum) =>
	http.get(
		`/jw-cloud-maintain/v1/maintain/WorkOrderLife/query`,
		{
			workOrderNum,
		},
		{
			header: {
				'Content-Type': 'application/x-www-form-urlencoded',
			},
		},
	);

/**
 * 工单指派人员查询
 * @param params
 * workOrderNum 工单编号
 * maintainUserId 维修人员id
 */
export const jwCloudAuth_SecurityCenter_System_User_GetUserListByIsAssign_GET = (params) =>
	http.get(`/jw-cloud-auth/security-center/system/user/getUserListByIsAssign`, params, {
		header: {
			'Content-Type': 'application/x-www-form-urlencoded',
		},
	});

/**
 * 工单指派
 * @param params
 * maintenanceMan 维修人员
 * workOrderNum 工单编号
 */
export const jwCloudMaintain_V1_Maintain_WorkOrder_Assign_POST = (params) =>
	http.post(`/jw-cloud-maintain/v1/maintain/WorkOrder/assign`, params, {
		header: {
			'Content-Type': 'application/x-www-form-urlencoded',
		},
	});

/**
 * 工单关闭
 * @param params
 * workOrderNum 工单编号
 */
export const jwCloudMaintain_V1_Maintain_WorkOrder_Close_POST = (params) =>
	http.post(`/jw-cloud-maintain/v1/maintain/WorkOrder/close`, params, {
		header: {
			'Content-Type': 'application/x-www-form-urlencoded',
		},
	});

/**
 * 工单处理
 * @param params
 * workOrderNum 工单编号
 * maintenanceResultDescription 维修结果描述
 * maintenanceResult 维修结果 1：已完成 2：未完成
 * maintenancePicture 维修图片
 */
export const jwCloudMaintain_V1_Maintain_WorkOrder_Handle_POST = (params) =>
	http.post(`/jw-cloud-maintain/v1/maintain/WorkOrder/handle`, params, {
		header: {
			'Content-Type': 'application/x-www-form-urlencoded',
		},
	});

/**
 * 查询乙方列表【房客列表】
 * @param params
 */
export const jwAppApi_V1_Contract_PagePartyTwo_GET = (params) =>
	http.get(`/jw-app-api/v1/contract/pagePartyTwo`, params, {
		header: {
			'Content-Type': 'application/x-www-form-urlencoded',
		},
	});

/**
 * 查询合同
 * @param params
 * pageNum 页码
 * pageSize 每页数量
 */
export const jwCloudPedestal_V1_Contract_Page_GET = (params) =>
	http.get(`/jw-app-api/v1/contract/page`, params, {
		header: {
			'Content-Type': 'application/x-www-form-urlencoded',
		},
	});

/**
 * 获取房间列表 - 未分页
 * @param params
 * communityId 园区id
 * term 期数名称
 * buildingNo 楼栋编号/也是楼幢号
 * unit 单元编号/也是单元号
 * houseTypeId 户型id
 * houseStatus 房间状态 -1:冻结 0:空闲 1:出租
 * houseId 房间id
 */
export const jwCloudPedestal_V1_Room_List_GET = (params) =>
	http.get(`/jw-app-api/v1/house/list`, params, {
		header: {
			'Content-Type': 'application/x-www-form-urlencoded',
		},
	});

/**
 * 获取合同预览地址
 * @param contractId 合同ID
 */
export const jwCloudPedestal_V1_Contract_GetPreviewUrl_GET = (contractId) =>
	http.get(
		`/jw-app-api/v1/contract/getPreviewUrl`,
		{
			contractId,
		},
		{
			header: {
				'Content-Type': 'application/x-www-form-urlencoded',
			},
		},
	);

/**
 * 获取房屋信息列表(按楼层分组)
 * @param params /v1/house/houseByFloor
 * @returns
 */
export const jwCloudPedestal_V1_House_HouseByFloor_GET = (params) =>
	http.get(`/jw-app-api/v1/house/houseByFloor`, params, {
		header: {
			'Content-Type': 'application/x-www-form-urlencoded',
		},
	});

/**
 * 获取区域下的期数
 * @param areaId 园区id
 */
export const jwCloudPedestal_V1_Room_AllTerms_GET = (areaId) =>
	http.get(
		`/jw-app-api/v1/house/allTerms`,
		{
			communityId: areaId,
		},
		{
			header: {
				'Content-Type': 'application/x-www-form-urlencoded',
			},
		},
	);

/**
 * 获取期数下的楼栋
 * @param communityId 园区id
 * @param term 期数名称
 */
export const jwCloudPedestal_V1_Room_AllBuildingNos_GET = (communityId, term) =>
	http.get(
		`/jw-app-api/v1/house/allBuildingNos`,
		{
			communityId,
			term,
		},
		{
			header: {
				'Content-Type': 'application/x-www-form-urlencoded',
			},
		},
	);

/**
 * 获取楼栋下的单元
 * @param communityId 园区id
 * @param term 期数名称
 * @param buildingNo 楼栋编号/也是楼幢号
 */
export const jwCloudPedestal_V1_Room_AllUnitNos_GET = (communityId, term, buildingNo) =>
	http.get(
		`/jw-app-api/v1/house/allUnits`,
		{
			communityId,
			term,
			buildingNo,
		},
		{
			header: {
				'Content-Type': 'application/x-www-form-urlencoded',
			},
		},
	);

/**
 * 冻结或解冻房屋
 * @param houseId 房屋id
 * @param operate 是否冻结 0：解冻 -1：冻结
 */
export const jwAppApi_V1_House_FreezeOrThaw_POST = (params) =>
	http.post(`/jw-app-api/v1/house/freezeOrThaw`, params, {
		header: {
			'Content-Type': 'application/x-www-form-urlencoded',
		},
	});
