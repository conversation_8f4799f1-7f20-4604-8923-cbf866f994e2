import { useUserStore } from '@/stores';

/**
 * 是否是工单权限的管理员
 * @returns true/false
 */
export const isWorkOrderAdmin = () => {
	const userStore = useUserStore();
	const userInfo = userStore.getUserInfo();
	if (userInfo.userRoles && userInfo?.userRoles.length > 0) {
		const isAdmin = userInfo.userRoles.some((item) => item?.roleSign === 'MAINTAIN_SUPERVISOR');
		return isAdmin;
	}
	return userInfo.admin;
};
