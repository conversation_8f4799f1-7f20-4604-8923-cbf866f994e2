.tenant-detail {
	// background-color: #f5f6f7;
	min-height: 100vh;
	padding-bottom: 20px;

	// 合同基本信息头部
	.tenant-header {
		background-color: #fff;
		padding: 30px 20px 20px 20px;
		margin-bottom: 20px;

		.tenant-info {
			.tenant-info-row {
				display: flex;
				align-items: center;
				margin-bottom: 20px;

				.tenant-label {
					font-size: 28px;
					color: #333;
					font-weight: bold;
				}

				.tenant-value {
					font-size: 28px;
					color: #333;
					font-weight: bold;
					flex: 1;
				}

				.tenant-preview {
					font-size: 28px;
					color: #397fef;
					text-decoration: underline;
					padding-left: 16px;
				}
			}
			.tenant-info-row-another {
				display: flex;
				align-items: center;
				justify-content: space-between;
				margin-bottom: 20px;

				.tenant-label {
					font-size: 28px;
					color: #333;
					font-weight: bold;
				}

				.tenant-status {
					font-size: 28px;
					&.status-pending {
						color: #ff9800;
					}

					&.status-active {
						color: #2196f3;
					}

					&.status-completed {
						color: #4caf50;
					}

					&.status-other {
						color: #607d8b;
					}
				}

				.tenant-count {
					font-size: 28px;
					color: #397fef;
				}
			}
		}
	}

	// 自定义Tab导航
	.custom-tab-header {
		background-color: #fff;
		border-bottom: 1px solid #f0f0f0;
		position: sticky;
		top: 0;
		z-index: 100;

		.tab-nav {
			display: flex;
			height: 88px;

			.tab-item {
				flex: 1;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				position: relative;
				cursor: pointer;

				.tab-title {
					font-size: 32px;
					color: #666;
					transition: color 0.3s ease;
				}

				.tab-line {
					position: absolute;
					bottom: 0;
					width: 50%; // 线条宽度为标题的一半
					height: 6px;
					background-color: #397fef;
					border-radius: 2px;
				}

				&.active {
					.tab-title {
						color: #397fef;
						font-weight: bold;
					}
				}

				// 点击效果
				&:active {
					background-color: rgba(57, 127, 239, 0.1);
				}
			}
		}
	}

	// 内容区域
	.tab-content {
		.tab-pane {
			padding: 20px;

			.content-section {
				background-color: #fff;
				border-radius: 12px;
				padding: 30px 20px 0 20px;
				margin-bottom: 20px;

				.section-title {
					font-size: 36px;
					font-weight: bold;
					color: #333;
					display: flex;
					align-items: center;
					gap: 10px;
					margin-bottom: 20px;
					image {
						width: 50px;
						height: 50px;
					}
				}

				// 信息列表样式
				.info-list {
					width: 100%;
					display: flex;
					align-items: center;
					flex-direction: column;
					box-sizing: border-box;
					background-color: #fff;
					.info-item {
						width: 100%;
						border-bottom: 2px solid #f5f6f7;
						padding: 30px;
						display: flex;
						align-items: center;
						justify-content: space-between;

						.info-label {
							font-size: 28px;
							color: #000;
						}
						.info-value {
							font-size: 28px;
							color: #333;
						}
					}
				}
			}

			// 信用档案样式
			.credit-info {
				width: 100%;
				display: flex;
				align-items: center;
				flex-direction: column;
				box-sizing: border-box;
				background-color: #fff;
				.credit-item {
					width: 100%;
					border-bottom: 2px solid #f5f6f7;
					padding: 30px;
					display: flex;
					align-items: center;
					justify-content: space-between;

					.credit-label {
						font-size: 28px;
						color: #000;
					}
					.credit-value {
						font-size: 28px;
						color: #333;
					}
				}
			}

			// 租赁信息样式
			.rental-info {
				.rental-header {
					display: flex;
					align-items: flex-start;
					margin-bottom: 20px;
					gap: 10px;
					.rental-title {
						font-size: 28px;
						font-weight: bold;
						color: #333;
					}
				}

				.room-info {
					border-top: 1px solid #f0f0f0;
					padding-top: 30px;
					.room-header {
						display: flex;
						align-items: center;
						justify-content: space-between;

						.room-title {
							font-size: 28px;
							font-weight: bold;
							color: #397fef;
						}
					}

					.fee-list {
						.fee-item {
							width: 100%;
							border-bottom: 2px solid #f5f6f7;
							padding: 30px 0;
							height: 50px;
							display: flex;
							align-items: center;
							justify-content: space-between;
							gap: 20px;

							.fee-label {
								font-size: 28px;
								color: #333;
							}

							.fee-value {
								display: flex;
								flex-direction: column;
								align-items: flex-end;
								.fee-value-title {
									font-size: 28px;
									color: #333;
									// 太长使用省略号，限制显示一行，超出显示省略号
									-webkit-line-clamp: 1;
									line-clamp: 1;
									overflow: hidden;
									text-overflow: ellipsis;
									display: -webkit-box;
									-webkit-box-orient: vertical;
								}
								.fee-value-subtitle {
									font-size: 24px;
									color: #999;
								}
							}
						}
					}
				}
			}

			// 空状态样式
			.empty-state {
				text-align: center;
				padding: 100px 0;
				color: #999;
				font-size: 28px;
			}
		}
	}
}
