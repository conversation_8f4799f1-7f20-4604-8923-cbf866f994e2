<template>
	<view class="tenant-detail">
		<!-- 合同基本信息头部 -->
		<view class="tenant-header">
			<view class="tenant-info">
				<view class="tenant-info-row">
					<text class="tenant-label">当前合同编码：</text>
					<text class="tenant-value">{{ contractInfo.contractId || '' }}</text>
					<template v-if="[1, 2, 3, 4].includes(contractInfo.contractStatus)">
						<Loading1
							v-if="isPreviewLoading"
							size="16px"
							style="color: #397fef; margin-left: 5px; font-weight: bold"
						/>
						<view v-else class="tenant-preview" @click="previewContactTemplateFile">
							预览
						</view>
					</template>
				</view>
				<view class="tenant-info-row">
					<text class="tenant-label">合同期限：</text>
					<text class="tenant-value">{{ formatContractPeriod() }}</text>
				</view>
				<view class="tenant-info-row-another">
					<text class="tenant-label">合同状态</text>
					<text
						class="tenant-status"
						:class="getStatusClass(contractInfo.contractStatus)"
					>
						{{ contractStatusMap[contractInfo.contractStatus] }}
					</text>
				</view>
				<view class="tenant-info-row-another">
					<text class="tenant-label">合同账单</text>
					<text class="tenant-count">共{{ contractInfo?.billCount || 1 }}个</text>
				</view>
			</view>
		</view>

		<!-- 自定义Tab导航 -->
		<view class="custom-tab-header">
			<view class="tab-nav">
				<view
					class="tab-item"
					:class="{ active: activeTab === 'signatory' }"
					@click="switchTab('signatory')"
				>
					<text class="tab-title">签约人信息</text>
					<view class="tab-line" v-if="activeTab === 'signatory'"></view>
				</view>
				<view
					class="tab-item"
					:class="{ active: activeTab === 'rental' }"
					@click="switchTab('rental')"
				>
					<text class="tab-title">租赁信息</text>
					<view class="tab-line" v-if="activeTab === 'rental'"></view>
				</view>
				<view
					class="tab-item"
					:class="{ active: activeTab === 'records' }"
					@click="switchTab('records')"
				>
					<text class="tab-title">合同记录</text>
					<view class="tab-line" v-if="activeTab === 'records'"></view>
				</view>
			</view>
		</view>

		<!-- 内容区域 -->
		<view class="tab-content">
			<!-- 签约人信息 -->
			<view v-if="activeTab === 'signatory'" class="tab-pane">
				<view class="content-section">
					<view class="section-title">
						<image src="@/assets/images/index_resident.png" />
						<text>签约人信息</text>
					</view>
					<view class="info-list">
						<view class="info-item">
							<view class="info-label">
								<text>{{ contractInfo.partyTwoName || '-' }}</text>
								<text>（房主）</text>
							</view>
							<view class="info-value" style="color: #4caf50">已实名认证</view>
						</view>
						<view class="info-item">
							<view class="info-label">电话</view>
							<view
								class="info-value"
								style="color: #397fef"
								@click="onPhoneNumberHandler(contractInfo.partyTwoPhoneNumber)"
							>
								{{ contractInfo.partyTwoPhoneNumber || '-' }}
							</view>
						</view>

						<view class="info-item">
							<view class="info-label">入住周期</view>
							<view class="info-value">
								{{ contractInfo.contractStartTime?.split(' ')[0] }} 至
								{{ contractInfo.contractEndTime?.split(' ')[0] }}
							</view>
						</view>
						<view class="info-item">
							<view class="info-label">当前入住天数</view>
							<view class="info-value">{{ getTenantStayDays(contractInfo) }}天</view>
						</view>
					</view>
				</view>

				<view class="content-section">
					<view class="section-title">
						<image src="@/assets/images/index_emcon.png" />
						<text>紧急联系人</text>
					</view>
					<view class="credit-info">
						<view class="credit-item">
							<text class="credit-label">姓名</text>
							<text class="credit-value">{{ contractInfo.contactName || '-' }}</text>
						</view>
						<view class="credit-item">
							<text class="credit-label">联系电话</text>
							<view
								class="credit-value"
								style="color: #397fef"
								@click="onPhoneNumberHandler(contractInfo.contactNumber)"
							>
								{{ contractInfo.contactNumber || '-' }}
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 租赁信息 -->
			<view v-if="activeTab === 'rental'" class="tab-pane">
				<view class="content-section">
					<view class="rental-info">
						<view class="rental-header">
							<view style="width: 20px; height: 20px">
								<Home color="#397fef" />
							</view>
							<text class="rental-title">{{ areaInfo?.areaName || '-' }}</text>
						</view>
						<view class="rental-header">
							<view style="width: 20px; height: 20px">
								<Location2 color="#397fef" />
							</view>
							<text class="rental-title">{{ areaInfo?.areaAddress || '-' }}</text>
						</view>
						<view class="room-info">
							<view class="section-title">租赁空间及费用</view>
							<view class="room-header">
								<text class="room-title">{{ formatRoomInfo() }}</text>
							</view>

							<view class="fee-list">
								<view class="fee-item">
									<view class="fee-label">房间面积</view>
									<view class="fee-value">
										<text class="fee-value-title">
											{{ roomInfo.area || '-' }}㎡
										</text>
									</view>
								</view>
								<view class="fee-item">
									<text class="fee-label">租金价格</text>
									<view class="fee-value">
										<text class="fee-value-title" style="color: #ff6b35">
											{{ formatMoneyWithCommas(rentPrice, 2) }}元/月
										</text>
									</view>
								</view>
								<view
									class="fee-item"
									v-if="roomPriceConfig?.housePriceTemplateGeRenYaJin?.isEnable"
								>
									<view class="fee-label">个人押金</view>
									<view class="fee-value">
										<text class="fee-value-title" style="color: #559f53">
											押{{
												numberToChinese(
													roomPriceConfig?.housePriceTemplateGeRenYaJin
														?.number || 1,
												)
											}}
										</text>
									</view>
								</view>
								<view
									class="fee-item"
									v-if="roomPriceConfig?.housePriceTemplateQiYeYaJin?.isEnable"
								>
									<view class="fee-label">企业押金</view>
									<view class="fee-value">
										<text class="fee-value-title" style="color: #559f53">
											押{{
												numberToChinese(
													roomPriceConfig?.housePriceTemplateQiYeYaJin
														?.number || 1,
												)
											}}
										</text>
									</view>
								</view>
								<view
									class="fee-item"
									v-if="roomPriceConfig?.housePriceTemplateFuWuFei?.isEnable"
								>
									<view class="fee-label">服务费</view>
									<view class="fee-value">
										<text class="fee-value-title">
											{{
												roomPriceConfig?.housePriceTemplateFuWuFei
													?.payMethod
											}}
										</text>
										<text class="fee-value-subtitle">
											¥
											{{
												formatMoneyWithCommas(
													roomPriceConfig?.housePriceTemplateFuWuFei
														?.price || 0,
													2,
												)
											}}/月
										</text>
									</view>
								</view>
								<view
									class="fee-item"
									v-if="roomPriceConfig?.housePriceTemplateWuYeFei?.isEnable"
								>
									<view class="fee-label">物业费</view>
									<view class="fee-value">
										<text class="fee-value-title">
											{{
												roomPriceConfig?.housePriceTemplateWuYeFei
													?.payMethod
											}}
										</text>
										<text class="fee-value-subtitle">
											¥
											{{
												formatMoneyWithCommas(
													roomPriceConfig?.housePriceTemplateWuYeFei
														?.price || 0,
													2,
												)
											}}/月
										</text>
									</view>
								</view>
								<view
									class="fee-item"
									v-if="
										roomPriceConfig?.housePriceTemplateWangLuoAnZhuangFei
											?.isEnable
									"
								>
									<view class="fee-label">网络安装费</view>
									<view class="fee-value">
										<text class="fee-value-title" style="color: #559f53">
											{{
												formatMoneyWithCommas(
													roomPriceConfig
														?.housePriceTemplateWangLuoAnZhuangFei
														?.price || 0,
													2,
												)
											}}元
										</text>
									</view>
								</view>
								<view
									class="fee-item"
									v-if="roomPriceConfig?.housePriceTemplateKuanDaiFei?.isEnable"
								>
									<view class="fee-label">宽带费</view>
									<view class="fee-value">
										<text class="fee-value-title">
											{{
												roomPriceConfig?.housePriceTemplateKuanDaiFei
													?.payMethod
											}}
											({{
												roomPriceConfig?.housePriceTemplateKuanDaiFei
													?.baoZhangMethod || '-'
											}})
										</text>
										<text class="fee-value-subtitle">
											¥
											{{
												formatMoneyWithCommas(
													roomPriceConfig?.housePriceTemplateKuanDaiFei
														?.price || 0,
													2,
												)
											}}/月
										</text>
									</view>
								</view>
								<view
									class="fee-item"
									v-if="roomPriceConfig?.housePriceTemplateShuiFei?.isEnable"
								>
									<view class="fee-label">水费</view>
									<view class="fee-value">
										<text class="fee-value-title">
											{{
												roomPriceConfig?.housePriceTemplateShuiFei
													?.payMethod
											}}
											({{
												roomPriceConfig?.housePriceTemplateShuiFei
													?.chuZhangMethod || '-'
											}})
										</text>
										<text class="fee-value-subtitle">
											¥
											{{
												formatMoneyWithCommas(
													roomPriceConfig?.housePriceTemplateShuiFei
														?.price || 0,
													2,
												)
											}}/吨
										</text>
									</view>
								</view>
								<view
									class="fee-item"
									v-if="roomPriceConfig?.housePriceTemplateReShuiFei?.isEnable"
								>
									<view class="fee-label">热水费</view>
									<view class="fee-value">
										<text class="fee-value-title">
											{{
												roomPriceConfig?.housePriceTemplateReShuiFei
													?.payMethod
											}}
											({{
												roomPriceConfig?.housePriceTemplateReShuiFei
													?.chuZhangMethod || '-'
											}})
										</text>
										<text class="fee-value-subtitle">
											¥
											{{
												formatMoneyWithCommas(
													roomPriceConfig?.housePriceTemplateReShuiFei
														?.price || 0,
													2,
												)
											}}/吨
										</text>
									</view>
								</view>
								<view
									class="fee-item"
									v-if="roomPriceConfig?.housePriceTemplateDianFei?.isEnable"
								>
									<view class="fee-label">电费</view>
									<view class="fee-value">
										<text class="fee-value-title">
											{{
												roomPriceConfig?.housePriceTemplateDianFei
													?.payMethod
											}}
											({{
												roomPriceConfig?.housePriceTemplateDianFei
													?.chuZhangMethod || '-'
											}})
										</text>
										<text class="fee-value-subtitle">
											¥
											{{
												formatMoneyWithCommas(
													roomPriceConfig?.housePriceTemplateDianFei
														?.price || 0,
													2,
												)
											}}/度
										</text>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 合同记录 -->
			<view v-if="activeTab === 'records'" class="tab-pane">
				<nut-empty class="empty-state" description="暂无合同记录"></nut-empty>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
import { onMounted, ref, computed } from 'vue';
import './index.scss';
import { useAreaStore } from '@/stores';
import {
	numberToChinese,
	formatMoneyWithCommas,
	previewFile,
	currentTimestamp,
	dateTimeToTimestamp,
} from '@/utils/utils';
import {
	jwCloudPedestal_V1_Contract_Page_GET,
	jwCloudPedestal_V1_Room_List_GET,
	jwCloudPedestal_V1_Contract_GetPreviewUrl_GET,
} from '@/utils/api';
import Taro from '@tarojs/taro';
// 引入资源
import { Loading1, Home, Location2 } from '@nutui/icons-vue-taro';

// 使用 Pinia store
const areaStore = useAreaStore();
const areaInfo = computed(() => areaStore.getCurrentArea());
console.log('areaInfo:', areaInfo.value);

// Tab 状态管理
const activeTab = ref('signatory');

// 切换 Tab
const switchTab = (tab: string) => {
	activeTab.value = tab;
};

// 合同ID，必传的参数
const contractId = ref('');
// 房间号ID，必传的参数
const houseId = ref('');

onMounted(() => {
	const instance = Taro.getCurrentInstance();
	const params = instance.router?.params;
	if (params?.contractId) {
		contractId.value = params.contractId;
	}
	if (params?.houseId) {
		houseId.value = params.houseId;
	}
	getRoomList();
	getContractList();
});

// 房间信息
const roomInfo = ref<any>({});
// 房间价格配置
const roomPriceConfig = ref<any>({});
const getRoomList = async () => {
	try {
		const res = await jwCloudPedestal_V1_Room_List_GET({
			houseId: houseId.value,
		});
		if (res.length === 0) {
			throw new Error('未找到房间信息');
		}
		roomInfo.value = res[0];
		console.log('房间信息:', roomInfo.value);
		try {
			if (!res[0].housePriceTemplateBaseStr) {
				throw new Error('未找到房间价格配置');
			}
			roomPriceConfig.value = JSON.parse(res[0].housePriceTemplateBaseStr);
			// 房间价格配置
			if (
				roomPriceConfig.value.housePriceTemplateFangZu &&
				roomPriceConfig.value.housePriceTemplateFangZu.price
			) {
				paymentMethods.value.forEach((paymentMethod) => {
					paymentMethod.price = roomPriceConfig.value.housePriceTemplateFangZu.price;
				});
				paymentMethods.value[0].rentDiscount =
					roomPriceConfig.value.housePriceTemplateFangZu?.payDiscountYear || 1;
				paymentMethods.value[1].rentDiscount =
					roomPriceConfig.value.housePriceTemplateFangZu?.payDiscountHalfYear || 1;
				paymentMethods.value[2].rentDiscount =
					roomPriceConfig.value.housePriceTemplateFangZu?.payDiscountSeason || 1;
				paymentMethods.value[3].rentDiscount =
					roomPriceConfig.value.housePriceTemplateFangZu?.payDiscountMonth || 1;
			}
			console.log('房间价格配置:', roomPriceConfig.value);
		} catch (error) {
			console.error('解析房间价格配置失败:', error);
		}
	} catch (error) {
		console.error('获取房间信息失败:', error);
	}
};

// 合同信息
const contractInfo = ref<any>({});
const getContractList = async () => {
	try {
		const res = await jwCloudPedestal_V1_Contract_Page_GET({
			contractId: contractId.value,
		});
		if (res.length === 0) {
			throw new Error('未找到合同信息');
		}
		contractInfo.value = res.items[0];
		console.log('合同信息:', contractInfo.value);
	} catch (error) {
		console.error('获取合同信息失败:', error);
	}
};

// 格式化合同期限
const formatContractPeriod = () => {
	const contractStartTime = contractInfo.value.contractStartTime;
	const contractEndTime = contractInfo.value.contractEndTime;
	if (!contractStartTime || !contractEndTime) return '-';
	const startTime = contractStartTime.split(' ')[0];
	const endTime = contractEndTime.split(' ')[0];
	const start = startTime.replace(/-/g, '');
	const end = endTime.replace(/-/g, '');
	return `${start}-${end}`;
};

// 格式化合同状态
// 0-待审批 1-待乙方签署 2-待甲方签署 3-履行中 4-未生效 5-已失效 6-已作废
const getStatusClass = (status: number) => {
	switch (status) {
		case 0:
			return 'status-pending';
		case 1:
			return 'status-active';
		case 2:
			return 'status-active';
		case 3:
			return 'status-completed';
		default:
			return 'status-other';
	}
};

const contractStatusMap = {
	0: '待审批',
	1: '待乙方签署',
	2: '待甲方签署',
	3: '履行中',
	4: '未生效',
	5: '已失效',
	6: '已作废',
};

// 格式化房间信息
const formatRoomInfo = () => {
	if (
		roomInfo.value.term &&
		roomInfo.value.buildingNo &&
		roomInfo.value.unit &&
		roomInfo.value.gatehouseNo
	) {
		return `整租 | ${roomInfo.value.term}期${roomInfo.value.buildingNo}幢${roomInfo.value.unit}单元${roomInfo.value.gatehouseNo}室`;
	}
	return '整租 | -';
};

// 支付方式
const paymentMethods = ref([
	{ label: '付十二', value: 4, disabled: false, rentDiscount: 1, price: 0 },
	{ label: '付六', value: 3, disabled: false, rentDiscount: 1, price: 0 },
	{ label: '付三', value: 2, disabled: false, rentDiscount: 1, price: 0 },
	{ label: '付一', value: 1, disabled: false, rentDiscount: 1, price: 0 },
]);
// 租金价格
const rentPrice = computed(() => {
	// 根据支付方式，计算出实际的租金价格
	const paymentItem = paymentMethods.value.find(
		(item) => item.value === contractInfo.value.payType,
	);
	if (!paymentItem) {
		return 0;
	}
	return paymentItem.price * paymentItem.rentDiscount;
});

// 是否正在预览合同模板文件
const isPreviewLoading = ref(false);
// 预览合同模板文件
const previewContactTemplateFile = async () => {
	isPreviewLoading.value = true;
	// 等待500ms
	await new Promise((resolve) => setTimeout(resolve, 1500));
	if (!contractInfo.value.contractId) {
		isPreviewLoading.value = false;
		return;
	}
	try {
		const res = await jwCloudPedestal_V1_Contract_GetPreviewUrl_GET(
			contractInfo.value.contractId,
		);
		previewFile(res || '');
	} catch (error) {
		console.error('预览合同模板文件失败:', error);
		previewFile('');
	} finally {
		isPreviewLoading.value = false;
	}
};

// 拨打电话
const onPhoneNumberHandler = (phoneNumber: string) => {
	Taro.makePhoneCall({ phoneNumber });
};
// 计算租户停留天数
const getTenantStayDays = (item: any) => {
	const currentTime = currentTimestamp();
	if (currentTime > dateTimeToTimestamp(item.contractStartTime)) {
		const start = dateTimeToTimestamp(item.contractStartTime);
		const end = Math.min(currentTime, dateTimeToTimestamp(item.contractEndTime));
		const diff = end - start;
		return Math.floor(diff / (1000 * 60 * 60 * 24));
	}
	return 0;
};
</script>
