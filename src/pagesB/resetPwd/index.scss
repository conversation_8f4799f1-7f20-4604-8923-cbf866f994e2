.reset-pwd {
  min-height: 100vh;
  background-color: #f5f6f7;
  display: flex;
  flex-direction: column;

  .reset-pwd-content {
    flex: 1;
    padding: 40px 30px;
    display: flex;
    flex-direction: column;
    gap: 30px;

    .input-group {
      display: flex;
      flex-direction: column;
      gap: 15px;

      .input-wrapper {
        position: relative;
        display: flex;
        align-items: center;
        background-color: #fff;
        border-radius: 12px;
        border: 2px solid #e5e5e5;
        transition: border-color 0.3s ease;

        &:focus-within {
          border-color: #397fef;
        }

        .password-input {
          flex: 1;
          padding: 25px 20px;
          font-size: 28px;
          color: #333;
          background: transparent;
          border: none;
          outline: none;

          &::placeholder {
            color: #999;
          }
        }

        .eye-icon {
          padding: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          user-select: none;

          image {
            width: 40px;
            height: 40px;
          }
        }
      }
    }

    .password-rules {
      background-color: #fff3cd;
      border: 1px solid #ffeaa7;
      border-radius: 8px;
      padding: 20px;
      display: flex;
      flex-direction: column;
      gap: 8px;

      .rules-title {
        font-size: 26px;
        font-weight: 500;
        color: #856404;
      }

      .rules-content {
        font-size: 24px;
        color: #856404;
        line-height: 1.4;
      }
    }

    .submit-btn {
      background: linear-gradient(135deg, #397fef 0%, #2968d4 100%);
      border-radius: 12px;
      padding: 25px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 20px;
      box-shadow: 0 4px 12px rgba(57, 127, 239, 0.3);
      transition: all 0.3s ease;

      &:active {
        transform: translateY(1px);
        box-shadow: 0 2px 8px rgba(57, 127, 239, 0.3);
      }

      &.disabled {
        background: #ccc;
        box-shadow: none;
        cursor: not-allowed;

        &:active {
          transform: none;
        }

        .btn-text {
          color: #999;
        }
      }

      .btn-text {
        font-size: 32px;
        color: #fff;
      }
    }
  }
}
