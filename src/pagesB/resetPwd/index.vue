<template>
	<view class="reset-pwd">
		<view class="reset-pwd-content">
			<!-- 旧密码输入框 -->
			<view class="input-group">
				<view class="input-wrapper">
					<input
						v-model="formData.oldPassword"
						:password="!showOldPassword"
						class="password-input"
						placeholder="请输入旧密码"
						maxlength="16"
					/>
					<view class="eye-icon" @click="toggleOldPasswordVisibility">
						<image :src="showOldPassword ? IndexEyeClosePng : IndexEyePng" />
					</view>
				</view>
			</view>

			<!-- 新密码输入框 -->
			<view class="input-group">
				<view class="input-wrapper">
					<input
						v-model="formData.newPassword"
						:password="!showNewPassword"
						class="password-input"
						placeholder="请输入新密码"
						maxlength="16"
					/>
					<view class="eye-icon" @click="toggleNewPasswordVisibility">
						<image :src="showNewPassword ? IndexEyeClosePng : IndexEyePng" />
					</view>
				</view>
			</view>

			<!-- 确认新密码输入框 -->
			<view class="input-group">
				<view class="input-wrapper">
					<input
						v-model="formData.confirmPassword"
						:password="!showConfirmPassword"
						class="password-input"
						placeholder="请再次输入新密码"
						maxlength="16"
					/>
					<view class="eye-icon" @click="toggleConfirmPasswordVisibility">
						<image :src="showConfirmPassword ? IndexEyeClosePng : IndexEyePng" />
					</view>
				</view>
			</view>

			<!-- 密码规则提示 -->
			<view class="password-rules">
				<text class="rules-title">密码规则：</text>
				<text class="rules-content">6-16位数字、字母或字符组成</text>
			</view>

			<!-- 确认按钮 -->
			<view class="submit-btn" :class="{ disabled: !isFormValid }" @click="onSubmitHandler">
				<text class="btn-text">确认修改</text>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import Taro from '@tarojs/taro';
import './index.scss';
import CryptoJS from 'crypto-js';
import { jwCloudAuth_SecurityCenter_SetpasswdNew_POST } from '@/utils/api';
import { onLogoutHandler } from '@/utils/login';

// 引入图标组件
import IndexEyePng from '@/assets/images/index_eye.png';
import IndexEyeClosePng from '@/assets/images/index_eye_close.png';

// 表单数据
const formData = ref({
	oldPassword: '',
	newPassword: '',
	confirmPassword: '',
});

// 密码显示状态
const showOldPassword = ref(false);
const showNewPassword = ref(false);
const showConfirmPassword = ref(false);

// 切换密码显示状态
const toggleOldPasswordVisibility = () => {
	showOldPassword.value = !showOldPassword.value;
	console.log('showOldPassword:', showOldPassword.value);
};

const toggleNewPasswordVisibility = () => {
	showNewPassword.value = !showNewPassword.value;
};

const toggleConfirmPasswordVisibility = () => {
	showConfirmPassword.value = !showConfirmPassword.value;
};

// 密码验证规则
const isPasswordValid = (password: string) => {
	const passwordRegex = /^[a-zA-Z0-9!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]{6,16}$/;
	return passwordRegex.test(password);
};

// 表单验证
const isFormValid = computed(() => {
	const { oldPassword, newPassword, confirmPassword } = formData.value;

	return (
		oldPassword.length > 0 &&
		isPasswordValid(newPassword) &&
		newPassword === confirmPassword &&
		oldPassword !== newPassword
	);
});

// 提交处理
const onSubmitHandler = async () => {
	if (!isFormValid.value) {
		return;
	}

	const { oldPassword, newPassword, confirmPassword } = formData.value;

	// 验证新密码格式
	if (!isPasswordValid(newPassword)) {
		Taro.showToast({
			title: '新密码格式不正确',
			icon: 'none',
			duration: 2000,
		});
		return;
	}

	// 验证两次密码是否一致
	if (newPassword !== confirmPassword) {
		Taro.showToast({
			title: '两次输入的密码不一致',
			icon: 'none',
			duration: 2000,
		});
		return;
	}

	// 验证新旧密码不能相同
	if (oldPassword === newPassword) {
		Taro.showToast({
			title: '新密码不能与旧密码相同',
			icon: 'none',
			duration: 2000,
		});
		return;
	}

	try {
		Taro.showLoading({
			title: '修改中...',
		});

		console.log('oldPassword:', oldPassword);
		console.log('newPassword:', newPassword);

		// 调用重置密码API
		await jwCloudAuth_SecurityCenter_SetpasswdNew_POST(
			CryptoJS.MD5(oldPassword).toString(),
			CryptoJS.MD5(newPassword).toString(),
		);

		Taro.hideLoading();

		Taro.showToast({
			title: '密码修改成功',
			icon: 'success',
			duration: 2000,
		});

		// 退出登录
		onLogoutHandler();
	} catch (error) {
		Taro.hideLoading();
		console.error('重置密码失败:', error);
	}
};
</script>
