<template>
	<view class="visit-detail">
		<view class="visit-top">
			<view class="house-image">
				<image :src="getImageSrc(visitDetail?.houseType?.photo1)" mode="aspectFill" />
				<text>暂无图片</text>
			</view>
			<view class="house-info">
				<view class="house-title">
					<text class="house-name">
						{{ currentArea?.areaName || '-' }}
						<template v-if="visitDetail?.houseType">
							（{{ visitDetail.houseType?.name }}）
						</template>
					</text>
					<view
						class="status-tag"
						:class="`state_${visitState(visitDetail.examineStatus).value}`"
					>
						{{ visitState(visitDetail.examineStatus).text }}
					</view>
				</view>
				<view class="house-details">
					<text class="area">{{ visitDetail?.houseType?.area || '0' }}m²</text>
					<text class="room-type">
						{{ visitDetail?.houseType?.roomNum || '0' }}室{{
							visitDetail?.houseType?.hallNum || '0'
						}}厅
					</text>
				</view>
				<view class="price">
					<text class="price-symbol">¥</text>
					<text class="price-amount">
						{{ visitDetail?.houseType?.price || '0' }}
					</text>
					<text class="price-unit">/月</text>
				</view>
			</view>
		</view>
		<view class="visit-basic-info">
			<view class="basic-info-title">基本信息</view>
			<view class="basic-info-content">
				<view class="basic-info-item">
					<view class="item-label">联系人名称</view>
					<view class="item-value">{{ visitDetail?.visitorName }}</view>
				</view>
				<view class="basic-info-item">
					<view class="item-label">联系方式</view>
					<view class="item-value" style="color: #397fef" @click.stop="onCallHandler">
						{{ visitDetail?.visitorPhone }}
					</view>
				</view>
				<view class="basic-info-item">
					<view class="item-label">看房时间</view>
					<view class="item-value">{{ visitDetail?.visitDate }} 全天 (08:30-17:30)</view>
				</view>
			</view>
		</view>
		<view class="visit-more-info">
			<view class="more-info-title">更多信息</view>
			<view class="more-info-content">
				<view class="more-info-item">
					<view class="item-label">期望入住时间</view>
					<view class="item-value">{{ visitDetail?.expectTime }}</view>
				</view>
				<view class="more-info-item">
					<view class="item-label">意向楼层</view>
					<view class="item-value">{{ visitDetail?.expectFloor }}</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import './index.scss';
import { jwCloudPedestal_V1_VisitorsReservation_Page_GET } from '@/utils/api';
import Taro from '@tarojs/taro';
import { useAreaStore } from '@/stores';
import { getFullAvatarUrl } from '@/utils/utils';
// 使用 Pinia store
const areaStore = useAreaStore();
const currentArea = computed(() => areaStore.getCurrentArea());

const visitDetail = ref<any>({});

onMounted(() => {
	const id = Taro.getCurrentInstance().router?.params.id;
	if (id) {
		getVisitDetail(id);
	}
});

const getVisitDetail = async (id: string) => {
	const res = await jwCloudPedestal_V1_VisitorsReservation_Page_GET({
		pageNum: 1,
		pageSize: 1,
		id,
	});
	if (res.items.length) {
		visitDetail.value = res.items[0];
		console.log('visitDetail:', visitDetail.value);
	}
};

// 拨打电话
const onCallHandler = () => {
	Taro.makePhoneCall({
		phoneNumber: visitDetail.value.visitorPhone,
	})
		.then(() => {
			console.log('拨打电话成功');
		})
		.catch(() => {
			console.log('拨打电话失败');
		});
};

// 获取图片源，如果为空则返回默认图片
const getImageSrc = (photo: string) => {
	if (!photo) {
		return '';
	}
	// 这个图片是逗号url拼接的
	const photos = photo.split(',');
	if (photos.length > 0) {
		return getFullAvatarUrl(photos[0]) || '';
	}
	return '';
};

// 0-待指派,1-待处理,2-已处理
const visitState = (state: number) => {
	switch (state) {
		case 0:
			return { value: 0, text: '待约看' };
		case 1:
			return { value: 1, text: '已完成' };
		case 2:
			return { value: 2, text: '已取消' };
		default:
			return { value: -1, text: '未知' };
	}
};
</script>
