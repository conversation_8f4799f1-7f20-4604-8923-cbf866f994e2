.visit-detail {
	min-height: 100vh;
	background-color: #f5f6f7;
	// 顶部房屋信息区域
	.visit-top {
		margin-top: 20px;
		display: flex;
		padding: 20px;
		background-color: #fff;
		.house-image {
			width: 240px;
			height: 180px;
			border-radius: 8px;
			overflow: hidden;
			margin-right: 16px;
			flex-shrink: 0;
			background-color: #f5f6f7;
			position: relative;
			image {
				position: relative;
				width: 100%;
				height: 100%;
				z-index: 2;
			}
			text {
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -50%);
				color: #999;
				font-size: 24px;
				z-index: 1;
			}
		}

		.house-info {
			flex: 1;
			display: flex;
			flex-direction: column;
			justify-content: space-between;

			.house-title {
				display: flex;
				justify-content: space-between;
				align-items: flex-start;
				margin-bottom: 8px;

				.house-name {
					font-size: 32px;
					font-weight: bold;
					color: #333;
					flex: 1;
					line-height: 1.2;
					margin-right: 12px;
				}

				.status-tag {
					padding: 4px 12px;
					border-radius: 8px;
					font-size: 22px;
					color: #fff;
					flex-shrink: 0;

					&.state_0 {
						background-color: #ff6b35;
					}

					&.state_1 {
						background-color: #4caf50;
					}

					&.state_2 {
						background-color: #9e9e9e;
					}
				}
			}

			.house-details {
				display: flex;
				align-items: center;
				margin-bottom: 6px;

				.area,
				.room-type {
					font-size: 26px;
					color: #666;
					margin-right: 16px;
				}
			}

			.price {
				display: flex;
				align-items: baseline;

				.price-symbol {
					font-size: 28px;
					color: #ff6b35;
					font-weight: bold;
				}

				.price-amount {
					font-size: 36px;
					color: #ff6b35;
					font-weight: bold;
					margin-right: 4px;
				}

				.price-unit {
					font-size: 26px;
					color: #ff6b35;
				}
			}
		}
	}
	.visit-basic-info {
		.basic-info-title {
			font-size: 24px;
			color: #666;
			padding: 30px 20px 15px 20px;
		}
		.basic-info-content {
			width: 100%;
			display: flex;
			align-items: center;
			flex-direction: column;
			padding: 0 30px;
			box-sizing: border-box;
			background-color: #fff;

			.basic-info-item {
				width: 100%;
				border-bottom: 2px solid #f5f6f7;
				padding: 30px;
				display: flex;
				align-items: center;
				justify-content: space-between;
				gap: 20px;
				.item-label {
					font-size: 28px;
					color: #000;
				}
				.item-value {
					font-size: 28px;
					color: #333;
				}
			}
		}
	}
	.visit-more-info {
		.more-info-title {
			font-size: 24px;
			color: #666;
			padding: 30px 20px 15px 20px;
		}
		.more-info-content {
			width: 100%;
			display: flex;
			align-items: center;
			flex-direction: column;
			padding: 0 30px;
			box-sizing: border-box;
			background-color: #fff;

			.more-info-item {
				width: 100%;
				border-bottom: 2px solid #f5f6f7;
				padding: 30px;
				display: flex;
				align-items: center;
				justify-content: space-between;
				gap: 20px;
				.item-label {
					font-size: 28px;
					color: #000;
				}
				.item-value {
					font-size: 28px;
					color: #333;
				}
			}
		}
	}
}
