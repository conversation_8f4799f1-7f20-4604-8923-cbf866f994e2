<template>
	<view class="deal-work-order">
		<view class="work-order-basic-info">
			<view class="basic-info-item">
				<view class="item-label">
					<text>工单名称</text>
				</view>
				<view class="item-value" @click="onWorkOrderDetailHandler">
					<text style="color: #397fef">{{ workOrderDetail.workOrderName }}</text>
				</view>
			</view>
			<view class="basic-info-item">
				<view class="item-label">
					<text>维修状态</text>
					<Issue color="#f44336" size="13" @click="onIssueHandler" />
				</view>
				<view class="item-value">
					<nut-radio-group v-model="maintenanceResult" direction="horizontal">
						<nut-radio label="1">完成</nut-radio>
						<nut-radio label="0">无法处理</nut-radio>
					</nut-radio-group>
				</view>
			</view>
		</view>
		<view class="work-order-desc-info-tip">
			<text style="color: red">*</text>
			<text>维修说明</text>
		</view>
		<view class="work-order-desc-info">
			<view class="work-order-desc-info-input">
				<NutTextarea
					style="height: 110px"
					v-model="maintenanceResultDescription"
					placeholder="请输入维修结果说明"
					:border="false"
				/>
			</view>
		</view>
		<view class="work-order-pic-info-tip">
			<text>维修图片（至多上传9张）</text>
		</view>
		<view class="work-order-pic-info">
			<!-- 图片上传网格 -->
			<view class="image-upload-grid">
				<!-- 已上传的图片 -->
				<view
					v-for="(image, index) in uploadedImages"
					:key="index"
					class="image-item"
					:class="{
						uploading: image.uploadStatus === 'uploading',
						'upload-failed': image.uploadStatus === 'failed',
						'upload-success': image.uploadStatus === 'success',
					}"
				>
					<img
						:src="image.url"
						class="uploaded-image"
						mode="aspectFill"
						@click="previewImage(index)"
					/>

					<!-- 删除按钮 -->
					<view
						class="delete-btn"
						@click="deleteImage(index)"
						v-if="image.uploadStatus !== 'uploading'"
					>
						<Close size="10" color="#fff" />
					</view>

					<!-- 上传状态遮罩 -->
					<view v-if="image.uploadStatus === 'uploading'" class="upload-overlay">
						<view class="upload-progress">
							<view class="progress-circle"></view>
						</view>
					</view>

					<!-- 上传失败状态 -->
					<view v-if="image.uploadStatus === 'failed'" class="upload-failed-overlay">
						<view class="failed-icon">!</view>
						<text class="failed-text">上传失败</text>
						<view class="retry-btn" @click.stop="retrySingleImage(index)">
							<text>重试</text>
						</view>
					</view>
				</view>

				<!-- 上传按钮 -->
				<view
					v-if="uploadedImages.length < maxImages"
					class="upload-btn"
					@click="showUploadOptions"
				>
					<view class="upload-icon">+</view>
					<text class="upload-text">上传照片</text>
				</view>
			</view>
		</view>
		<view class="deal-work-order-footer">
			<view
				class="footer-btn"
				@click="onSubmitHandler"
				:style="{ backgroundColor: isDone ? '#397fef' : '#ccc' }"
			>
				完成
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
import { onMounted, ref, computed } from 'vue';
import './index.scss';
import Taro from '@tarojs/taro';
import { useWorkOrderStore } from '@/stores';

import {
	jwCloudMaintain_V1_Maintain_WorkOrder_Page_GET,
	jwCloudPedestal_V1_Fastdfs_UploadFile_POST,
	jwCloudMaintain_V1_Maintain_WorkOrder_Handle_POST,
} from '@/utils/api';

// 引入资源
import { Issue, Close } from '@nutui/icons-vue-taro';

// 使用 Pinia store
const workOrderStore = useWorkOrderStore();

const onWorkOrderDetailHandler = () => {
	Taro.navigateTo({
		url: '/pagesB/workOrder/workOrderDetail/index?workOrderNum=' + workOrderNum.value,
	});
};

// 工单编号
const workOrderNum = ref('');
onMounted(() => {
	// 获取工单编号
	const options = Taro.getCurrentInstance().router?.params;
	if (options?.workOrderNum) {
		workOrderNum.value = options.workOrderNum;
		getWorkOrderDetail();
	}
});

const workOrderDetail = ref<any>({});
const getWorkOrderDetail = async () => {
	try {
		const res = await jwCloudMaintain_V1_Maintain_WorkOrder_Page_GET({
			workOrderNum: workOrderNum.value,
		});
		if (res.items.length > 0) {
			workOrderDetail.value = {
				...res.items[0],
			};
			console.log('工单详情', workOrderDetail.value);
		}
	} catch (error) {
		console.error('获取工单详情失败:', error);
	}
};

const onIssueHandler = () => {
	Taro.showToast({
		title: '无法处理后该工单将流转回派发人',
		icon: 'none',
	});
};

// 维修状态 0：无法处理 1：完成
const maintenanceResult = ref('1');

// 维修说明
const maintenanceResultDescription = ref('');

// 图片上传相关
interface UploadedImage {
	url: string;
	tempFilePath?: string;
	name?: string;
	size?: number;
	uploadStatus?: 'pending' | 'uploading' | 'success' | 'failed';
	uploadedUrl?: string; // 上传成功后的服务器URL
	uploadError?: string; // 上传错误信息
}

// 上传队列管理
interface UploadTask {
	id: string;
	image: UploadedImage;
	retryCount: number;
	maxRetries: number;
}

const maxImages = 9; // 最大上传图片数量
const uploadedImages = ref<UploadedImage[]>([]); // 已上传的图片列表

// 显示上传选项菜单
const showUploadOptions = () => {
	Taro.showActionSheet({
		itemList: ['拍照', '从相册选择'],
		success: (res) => {
			if (res.tapIndex === 0) {
				// 拍照
				chooseImageFromCamera();
			} else if (res.tapIndex === 1) {
				// 从相册选择
				chooseImageFromAlbum();
			}
		},
		fail: (err) => {
			console.log('用户取消选择:', err);
		},
	});
};

// 从相机拍照
const chooseImageFromCamera = () => {
	const remainingCount = maxImages - uploadedImages.value.length;

	Taro.chooseImage({
		count: Math.min(1, remainingCount), // 拍照只能选择1张
		sizeType: ['compressed'], // 压缩图
		sourceType: ['camera'], // 只允许拍照
		success: (res) => {
			handleImageSuccess(res);
		},
		fail: (err) => {
			console.error('拍照失败:', err);
		},
	});
};

// 从相册选择
const chooseImageFromAlbum = () => {
	const remainingCount = maxImages - uploadedImages.value.length;

	Taro.chooseImage({
		count: remainingCount, // 剩余可选择数量
		sizeType: ['compressed'], // 压缩图
		sourceType: ['album'], // 只允许从相册选择
		success: (res) => {
			handleImageSuccess(res);
		},
		fail: (err) => {
			console.error('选择图片失败:', err);
		},
	});
};

// 处理图片选择成功
const handleImageSuccess = async (res: any) => {
	const tempFilePaths = res.tempFilePaths;
	const tempFiles = res.tempFiles || [];

	// 验证图片数量
	const remainingSlots = maxImages - uploadedImages.value.length;
	if (tempFilePaths.length > remainingSlots) {
		Taro.showToast({
			title: `最多只能上传${maxImages}张图片`,
			icon: 'none',
		});
		return;
	}

	// 检查网络状态
	const networkOk = await checkNetworkStatus();
	if (!networkOk) {
		return;
	}

	// 创建图片数据并立即开始上传
	const newImages: UploadedImage[] = [];

	tempFilePaths.forEach((filePath: string, index: number) => {
		const fileInfo = tempFiles[index] || {};

		// 验证图片大小（限制为5MB）
		const maxSize = 5 * 1024 * 1024; // 5MB
		if (fileInfo.size && fileInfo.size > maxSize) {
			Taro.showToast({
				title: '图片大小不能超过5MB',
				icon: 'none',
			});
			return;
		}

		const imageData: UploadedImage = {
			url: filePath,
			tempFilePath: filePath,
			name: fileInfo.name || `image_${Date.now()}_${index}.jpg`,
			size: fileInfo.size || 0,
			uploadStatus: 'pending', // 初始状态为待上传
		};

		newImages.push(imageData);
		uploadedImages.value.push(imageData);
	});

	console.log('已选择图片:', newImages);

	// 立即开始上传新选择的图片
	if (newImages.length > 0) {
		// 为每张新图片创建上传任务并立即开始上传
		newImages.forEach(async (image, index) => {
			await uploadImageImmediately(image, index);
		});
	}
};

// 删除图片
const deleteImage = (index: number) => {
	Taro.showModal({
		title: '提示',
		content: '确定要删除这张图片吗？',
		success: (res) => {
			if (res.confirm) {
				uploadedImages.value.splice(index, 1);
			}
		},
	});
};

// 预览图片
const previewImage = (index: number) => {
	console.log('uploadedImages:', uploadedImages.value);
	const urls = uploadedImages.value.map((img) => img.url);

	Taro.previewImage({
		urls: urls,
		current: urls[index],
	});
};

// 检查网络状态
const checkNetworkStatus = async (): Promise<boolean> => {
	try {
		const networkInfo = await Taro.getNetworkType();
		if (networkInfo.networkType === 'none') {
			Taro.showToast({
				title: '网络连接不可用',
				icon: 'none',
			});
			return false;
		}

		if (networkInfo.networkType === '2g') {
			const result = await Taro.showModal({
				title: '网络提示',
				content: '当前网络较慢，上传可能需要较长时间，是否继续？',
				confirmText: '继续上传',
				cancelText: '取消',
			});
			return result.confirm;
		}

		return true;
	} catch (error) {
		console.warn('获取网络状态失败:', error);
		return true; // 获取失败时默认允许上传
	}
};

// 立即上传单张图片（选择后立即上传）
const uploadImageImmediately = async (image: UploadedImage, index: number): Promise<void> => {
	try {
		// 更新图片状态为上传中
		const imageIndex = uploadedImages.value.findIndex(
			(img) => img.tempFilePath === image.tempFilePath,
		);
		if (imageIndex !== -1) {
			uploadedImages.value[imageIndex].uploadStatus = 'uploading';
		}

		// 设置超时时间 30秒
		const timeout = setTimeout(() => {
			throw new Error('上传超时');
		}, 30000);

		try {
			const result = await jwCloudPedestal_V1_Fastdfs_UploadFile_POST(image.tempFilePath!);
			clearTimeout(timeout);

			// 更新图片状态为成功
			if (imageIndex !== -1) {
				uploadedImages.value[imageIndex].uploadStatus = 'success';
				uploadedImages.value[imageIndex].uploadedUrl = result as string;
			}

			console.log(`图片 ${image.name} 上传成功:`, result);
		} catch (error) {
			clearTimeout(timeout);

			// 更新图片状态为失败
			if (imageIndex !== -1) {
				uploadedImages.value[imageIndex].uploadStatus = 'failed';
				uploadedImages.value[imageIndex].uploadError =
					(error as Error).message || '上传失败';
			}

			console.error(`图片第${index + 1}张，名字叫 ${image.name} 上传失败:`, error);
		}
	} catch (error) {
		console.error('上传过程中发生错误:', error);
	}
};

// 单张图片上传（用于批量上传和重试）
const uploadSingleImage = async (task: UploadTask): Promise<string> => {
	const { image } = task;

	return new Promise((resolve, reject) => {
		// 设置超时时间 30秒
		const timeout = setTimeout(() => {
			reject(new Error('上传超时'));
		}, 30000);

		jwCloudPedestal_V1_Fastdfs_UploadFile_POST(image.tempFilePath!)
			.then((result) => {
				clearTimeout(timeout);

				// 更新图片状态
				const imageIndex = uploadedImages.value.findIndex(
					(img) => img.tempFilePath === image.tempFilePath,
				);
				if (imageIndex !== -1) {
					uploadedImages.value[imageIndex].uploadStatus = 'success';
					uploadedImages.value[imageIndex].uploadedUrl = result as string;
				}

				resolve(result as string);
			})
			.catch((error) => {
				clearTimeout(timeout);

				// 更新图片状态
				const imageIndex = uploadedImages.value.findIndex(
					(img) => img.tempFilePath === image.tempFilePath,
				);
				if (imageIndex !== -1) {
					uploadedImages.value[imageIndex].uploadStatus = 'failed';
					uploadedImages.value[imageIndex].uploadError = error.message || '上传失败';
				}

				reject(error);
			});
	});
};

// 重试单张图片
const retrySingleImage = async (index: number): Promise<void> => {
	const image = uploadedImages.value[index];
	if (!image || image.uploadStatus !== 'failed') {
		return;
	}

	// 重置图片状态
	image.uploadStatus = 'pending';
	image.uploadError = undefined;

	// 检查网络状态
	const networkOk = await checkNetworkStatus();
	if (!networkOk) {
		return;
	}

	// 创建单个上传任务
	const task: UploadTask = {
		id: `retry_${Date.now()}_${index}`,
		image,
		retryCount: 0,
		maxRetries: 2,
	};

	try {
		image.uploadStatus = 'uploading';
		await uploadSingleImage(task);
	} catch (error) {
		console.error('重试上传失败:', error);
	}
};
const isDone = computed(() => {
	// 需要校验工单名称、工单类型
	if (!maintenanceResultDescription.value) {
		return false;
	}

	return true;
});
const onSubmitHandler = async () => {
	if (!isDone.value) {
		return;
	}
	Taro.showLoading({
		title: '提交中...',
	});
	// 等待500ms
	await new Promise((resolve) => setTimeout(resolve, 500));
	try {
		// 处理图片上传
		const uploadedUrls = await Promise.all(
			uploadedImages.value.map((image) => image.uploadedUrl),
		);
		// 处理表单数据
		const params = {
			workOrderNum: workOrderDetail.value.workOrderNum,
			maintenanceResult: maintenanceResult.value == '1' ? 1 : 0,
			maintenanceResultDescription: maintenanceResultDescription.value,
			maintenancePicture: uploadedUrls.join(','),
		};

		console.log('params：', params);
		await jwCloudMaintain_V1_Maintain_WorkOrder_Handle_POST(params);
		// 返回A页面时，标记需要刷新
		const pages = Taro.getCurrentPages();
		const prevPage = pages[pages.length - 2]; // 获取A页面实例
		if (prevPage.reloadPage && typeof prevPage.reloadPage === 'function') {
			Taro.showToast({
				title: '提交成功',
				icon: 'success',
			});
			workOrderStore.setUndoWorkOrderCount(workOrderStore.getUndoWorkOrderCount() - 1);
			// 延迟返回，让用户看到处理状态
			setTimeout(() => {
				prevPage.reloadPage();
				// 不需要返回了，因为用户在点击返回的时候已经触发过一次返回了
				Taro.navigateBack();
			}, 1000);
		}
	} catch (error) {
		Taro.showToast({
			title: '提交失败',
			icon: 'none',
		});
	} finally {
		Taro.hideLoading();
	}
};
</script>
