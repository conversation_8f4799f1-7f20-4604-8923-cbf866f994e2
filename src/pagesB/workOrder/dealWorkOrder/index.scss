.deal-work-order {
	background-color: #f5f6f7;
	height: 100vh;
	overflow-y: auto;
	box-sizing: border-box;
	padding: 20px;
	padding-bottom: calc(120px + env(safe-area-inset-bottom));
	display: flex;
	flex-direction: column;
	.work-order-basic-info {
		background-color: #fff;
		padding: 0px 20px;
		border-radius: 10px;
		box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
		display: flex;
		flex-direction: column;
		.basic-info-item {
			width: 100%;
			border-bottom: 2px solid #f5f6f7;
			padding: 30px 5px;
			display: flex;
			align-items: center;
			justify-content: space-between;
			box-sizing: border-box;
			.item-label {
				font-size: 30px;
				color: #000;
				margin-right: 30px;
				display: flex;
				align-items: center;
				gap: 10px;
			}
			.item-value {
				flex: 1;
				font-size: 30px;
				color: #333;
				display: flex;
				align-items: flex-end;
        justify-content: flex-end;
				gap: 5px;
			}
		}
	}

  .work-order-desc-info-tip {
		font-size: 24px;
		color: #666;
		display: flex;
		align-items: center;
		gap: 5px;
		padding: 30px 0 10px 0px;
	}

  .work-order-desc-info {
		background-color: #fff;
		padding: 20px;
		border-radius: 10px;
		box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
		display: flex;
		flex-direction: column;
		.work-order-desc-info-input {
			position: relative;
			height: 250px;
			background-color: #f2f1f5;
			padding: 15px;
			box-sizing: border-box;
			border-radius: 10px;
		}
	}
  .work-order-pic-info-tip {
		font-size: 24px;
		color: #666;
		display: flex;
		align-items: center;
		gap: 5px;
		padding: 30px 0 0 0;
	}

	.work-order-pic-info {
		padding: 30px 20px;
		box-sizing: border-box;

		.image-upload-grid {
			display: grid;
			grid-template-columns: repeat(3, 1fr);
			gap: 15px;
			width: 100%;
			box-sizing: border-box;

			.image-item {
				position: relative;
				aspect-ratio: 1 / 1;
				border-radius: 8px;
				overflow: hidden;
				background-color: #fff;
				box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

				.uploaded-image {
					width: 100%;
					height: 100%;
					object-fit: cover;
					border-radius: 8px;
				}

				.delete-btn {
					position: absolute;
					top: 0px;
					right: 0px;
					width: 40px;
					height: 40px;
					background-color: #ff4757;
					border-radius: 0 8px 0 8px;
					display: flex;
					align-items: center;
					justify-content: center;
					box-shadow: 0 2px 8px rgba(255, 71, 87, 0.3);
					z-index: 100;

					&:active {
						transform: scale(0.9);
						background-color: #ff3742;
					}
				}

				// 上传状态遮罩
				.upload-overlay {
					position: absolute;
					top: 0;
					left: 0;
					right: 0;
					bottom: 0;
					background-color: rgba(0, 0, 0, 0.6);
					border-radius: 8px;
					display: flex;
					align-items: center;
					justify-content: center;
					z-index: 50;

					.upload-progress {
						display: flex;
						flex-direction: column;
						align-items: center;
						gap: 8px;

						.progress-circle {
							width: 50px;
							height: 50px;
							border: 5px solid rgba(255, 255, 255, 0.3);
							border-top: 5px solid #397fef;
							border-radius: 50%;
							display: flex;
							align-items: center;
							justify-content: center;
							animation: spin 1s linear infinite;
						}
					}
				}

				// 上传失败遮罩
				.upload-failed-overlay {
					position: absolute;
					top: 0;
					left: 0;
					right: 0;
					bottom: 0;
					background-color: rgba(0, 0, 0, 0.6);
					border-radius: 8px;
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					gap: 8px;
					z-index: 50;

					.failed-icon {
						width: 48px;
						height: 48px;
						background-color: #fff;
						border-radius: 50%;
						display: flex;
						align-items: center;
						justify-content: center;
						color: #ff4757;
						font-size: 32px;
						font-weight: bold;
					}

					.failed-text {
						color: #fff;
						font-size: 24px;
						text-align: center;
					}

					.retry-btn {
						background-color: #fff;
						color: #ff4757;
						padding: 8px 24px;
						border-radius: 24px;
						font-size: 24px;
						margin-top: 8px;

						&:active {
							background-color: #f0f0f0;
						}
					}
				}


				// 上传状态样式
				&.uploading {
					.uploaded-image {
						opacity: 0.7;
					}
				}

				&.upload-failed {
					.uploaded-image {
						opacity: 0.5;
					}
				}

				&.upload-success {
					.uploaded-image {
						opacity: 1;
					}
				}
			}

			.upload-btn {
				aspect-ratio: 1 / 1;
				border: 2px dashed #397fef;
				border-radius: 8px;
				background-color: #f8fbff;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				cursor: pointer;
				transition: all 0.3s ease;

				.upload-icon {
					font-size: 48px;
					color: #397fef;
					font-weight: 300;
					line-height: 1;
					margin-bottom: 8px;
				}

				.upload-text {
					font-size: 28px;
					color: #397fef;
					text-align: center;
				}

				&:active {
					transform: scale(0.98);
					background-color: #e8f4ff;
					border-color: #2c6cd6;

					.upload-icon,
					.upload-text {
						color: #2c6cd6;
					}
				}

				// 悬停效果（H5端）
				&:hover {
					background-color: #e8f4ff;
					border-color: #2c6cd6;

					.upload-icon,
					.upload-text {
						color: #2c6cd6;
					}
				}
			}
		}
	}

  .deal-work-order-footer {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		background-color: #fff;
		padding: 20px 30px 0 30px;
		padding-bottom: calc(20px + env(safe-area-inset-bottom)); // 适配安全区域
		border-top: 1px solid #f0f0f0;
		z-index: 999;
		.footer-btn {
			font-size: 28px;
			color: #fff;
			background-color: #397fef;
			border-radius: 40px;
			padding: 20px 0;
			text-align: center;
		}
	}
  // nut的input输入框
	.nut-input {
		padding: 0;
		margin: 0;
	}
	.input-text {
		color: #333;
		font-size: 30px;
	}
	.nut-textarea {
		padding: 0;
		margin: 0;
		background-color: #f2f1f5;
	}
  --nut-radio-label-font-active-color: #397fef;
}
