.work-order-detail {
	background-color: #f5f6f7;
	height: 100vh;
	overflow-y: auto;
	box-sizing: border-box;
	padding: 20px;
	display: flex;
	flex-direction: column;
	gap: 30px;
	.work-order-detail-basic-info {
		background-color: #fff;
		padding: 20px;
		border-radius: 10px;
		box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
		display: flex;
		flex-direction: column;
		gap: 20px;
		.title {
			font-size: 44px;
			font-weight: bold;
			color: #333;
		}
		.item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			.label {
				font-size: 32px;
				color: #666;
				margin-right: 50px;
			}
			.value {
				flex: 1;
				font-size: 32px;
				color: #333;
				word-break: break-word;
				display: flex;
				align-items: center;
				justify-content: flex-end;
				text-align: right;
				line-height: 1.2;
				&.state_0 {
					color: #ff6b35;
				}
				&.state_1 {
					color: #f9a825;
				}
				&.state_2 {
					color: #4caf50;
				}
				&.state_3 {
					color: #9e9e9e;
				}
				&.state_4 {
					color: #f44336;
				}
				&.state_-1 {
					color: #9e9e9e;
				}
			}
		}
	}
	.work-order-detail-report-info {
		background-color: #fff;
		padding: 20px;
		border-radius: 10px;
		box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
		display: flex;
		flex-direction: column;
		gap: 30px;
		.title {
			font-size: 44px;
			font-weight: bold;
			color: #333;
		}
		.item {
			display: flex;
			flex-direction: column;
			gap: 10px;
			.label {
				font-size: 24px;
				color: #666;
				margin-right: 50px;
			}
			.content {
				position: relative;
				height: 250px;
				background-color: #f2f1f5;
				font-size: 32px;
				color: #333;
				padding: 15px;
				box-sizing: border-box;
				border-radius: 10px;
				.content-text {
					overflow: auto;
					word-wrap: normal; // 自动换行
					word-break: break-word; // 强制换行
					line-height: 1.2;
					height: 180px;
				}
				.content-time {
					position: absolute;
					font-size: 28px;
					color: #999;
					bottom: 10px;
					right: 20px;
				}
			}
			.image-list {
				display: grid;
				grid-template-columns: repeat(3, 1fr);
				gap: 20px;
				.image-item {
					position: relative;
					aspect-ratio: 1.2 / 1;
					image {
						position: absolute;
						width: 100%;
						height: 100%;
						border-radius: 10px;
					}
				}
			}
		}
	}
	.work-order-detail-repair-info {
		background-color: #fff;
		padding: 20px;
		border-radius: 10px;
		box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
		display: flex;
		flex-direction: column;
		gap: 20px;
		.title {
			font-size: 44px;
			font-weight: bold;
			color: #333;
		}
		.item {
			display: flex;
			flex-direction: column;
			gap: 10px;
			.label {
				font-size: 24px;
				color: #666;
				margin-right: 50px;
			}
			.content {
				position: relative;
				height: 250px;
				background-color: #f2f1f5;
				font-size: 32px;
				color: #333;
				padding: 15px;
				box-sizing: border-box;
				border-radius: 10px;
				.content-text {
					overflow: auto;
					word-wrap: normal; // 自动换行
					word-break: break-word; // 强制换行
					line-height: 1.2;
					height: 180px;
				}
				.content-time {
					position: absolute;
					font-size: 28px;
					color: #999;
					bottom: 10px;
					right: 20px;
				}
			}
			.image-list {
				display: grid;
				grid-template-columns: repeat(3, 1fr);
				gap: 20px;
				.image-item {
					position: relative;
					aspect-ratio: 1.2 / 1;
					image {
						position: absolute;
						width: 100%;
						height: 100%;
						border-radius: 10px;
					}
				}
			}
		}
	}
	.work-order-detail-history-info {
		background-color: #fff;
		padding: 20px;
		border-radius: 10px;
		box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
		display: flex;
		flex-direction: column;
		gap: 20px;
		.title {
			font-size: 44px;
			font-weight: bold;
			color: #333;
		}
		.content {
			display: flex;
			flex-direction: column;
			padding: 20px 20px 50px 20px;
			// 自定义步骤内容样式
			.step-custom-content {
				display: flex;
				flex-direction: column;
				gap: 10px;
				padding: 10px 0;
				margin-top: -10px; // 调整内容的偏移量
				// 确保容器自适应高度
				height: auto;
				min-height: auto;

				.step-text {
					font-size: 28px;
					color: #333;
					line-height: 1.4;
					word-break: break-word;
				}

				// 如果有图片
				.step-images {
					display: grid;
					grid-template-columns: repeat(3, 1fr);
					gap: 20px;
					.step-image {
						position: relative;
						aspect-ratio: 1.2 / 1;
						image {
							position: absolute;
							width: 100%;
							height: 100%;
							border-radius: 10px;
						}
					}
				}
			}
		}
		.loading-content {
			display: flex;
			align-items: center;
			justify-content: center;
			gap: 20px;
			padding: 50px 0;
			text {
				font-size: 32px;
				color: #999;
			}
		}
		.no-data {
			font-size: 32px;
			color: #999;
			text-align: center;
			padding: 50px 0;
		}
	}

	// NutUI Steps 样式自定义
	--nut-steps-wait-icon-bg-color: #397fef;

	.nut-steps-vertical.nut-steps-dot .nut-step-icon {
		width: 30px;
		height: 30px;
	}
	// 步骤线条样式
	.nut-step-line {
		background-color: #397fef;
		width: 7px;
		transform: translate(-50%, 0);
	}

	// 关键：让 nut-step 内容区域自适应高度
	.nut-step {
		// 移除固定高度限制
		min-height: auto !important;
		height: auto !important;

		// 内容区域自适应
		.nut-step-content {
			height: auto !important;
			min-height: auto !important;
			flex: none !important;
			// 确保内容完全显示
			overflow: visible !important;
			// 内容内部间距
			padding-bottom: 20px;
		}

		// 步骤主体区域
		.nut-step-main {
			height: auto !important;
			min-height: auto !important;
			flex: 1;
			padding-right: 0%;

			// 标题区域
			.nut-step-title {
				margin-bottom: 8px;
			}
		}

		// 连接线调整
		.nut-step-line {
			// 让连接线延伸到内容底部
			height: 100% important;
		}
	}
}
