<template>
	<view class="work-order-detail">
		<view class="work-order-detail-basic-info">
			<view class="title">基本信息</view>
			<view class="item">
				<text class="label">工单号</text>
				<text class="value" :user-select="true">
					{{ workOrderDetail?.workOrderNum || '-' }}
				</text>
			</view>
			<view class="item">
				<text class="label">名称</text>
				<text class="value" :user-select="true">
					{{ workOrderDetail?.workOrderName || '-' }}
				</text>
			</view>
			<view class="item">
				<text class="label">工单类型</text>
				<text class="value" style="color: #3e77ff" :user-select="true">
					{{ formatWorkType(workOrderDetail?.workOrderType) }}
				</text>
			</view>
			<view class="item">
				<text class="label">所在区域</text>
				<text class="value" :user-select="true">
					{{ workOrderDetail?.areaName || '-' }}
				</text>
			</view>
			<view class="item">
				<text class="label">维修人</text>
				<text class="value" :user-select="true">
					{{ workOrderDetail?.maintenanceMan || '-(暂未分配维修人员)' }}
				</text>
			</view>
			<view class="item">
				<text class="label">状态</text>
				<text
					class="value"
					:class="{ ['state_' + workOrderState(workOrderDetail?.state).value]: true }"
				>
					{{ workOrderState(workOrderDetail?.state).text }}
				</text>
			</view>
			<view class="item">
				<text class="label">联系方式</text>
				<text class="value" :user-select="true">
					{{ workOrderDetail?.connectWay || '-' }}
				</text>
			</view>
			<view class="item">
				<text class="label">报修户址</text>
				<text class="value" :user-select="true">
					{{ workOrderDetail?.repairAddress || '-' }}
				</text>
			</view>
			<view class="item">
				<text class="label">预约时间</text>
				<text class="value">
					{{ workOrderDetail?.appointmentTime || '-' }}
				</text>
			</view>
		</view>
		<view class="work-order-detail-report-info">
			<view class="title">上报信息</view>
			<view class="item">
				<text class="label">报修说明</text>
				<view class="content">
					<view class="content-text">
						{{ workOrderDetail?.workOrderDescription || '-' }}
					</view>
					<view class="content-time">
						{{ workOrderDetail?.reportTime || '-' }}
					</view>
				</view>
			</view>
			<view class="item" v-if="workOrderDetail?.reportPicture?.length > 0">
				<text class="label">上报图片</text>
				<view class="image-list">
					<view
						v-for="(item, index) in reportPictureList"
						:key="index"
						class="image-item"
					>
						<img
							:src="item"
							mode="aspectFill"
							@click="onPicturePreviewHandler(reportPictureList, index)"
							@longpress="onImageLongPress(reportPictureList, index)"
						/>
					</view>
				</view>
			</view>
		</view>
		<view
			class="work-order-detail-repair-info"
			v-if="![0, 1, 4].includes(workOrderDetail?.state)"
		>
			<view class="title">维修信息</view>
			<view class="item">
				<text class="label">维修说明</text>
				<view class="content">
					<view class="content-text">
						{{ workOrderDetail?.maintenanceResultDescription || '-' }}
					</view>
					<view class="content-time">
						{{ workOrderDetail?.maintenanceBeginTime || '-' }} ~
						{{ workOrderDetail?.maintenanceEndTime || '-' }}
					</view>
				</view>
			</view>
			<view class="item" v-if="workOrderDetail?.maintenancePicture?.length > 0">
				<text class="label">维修图片</text>
				<view class="image-list">
					<view
						v-for="(item, index) in repairPictureList"
						:key="index"
						class="image-item"
					>
						<img
							:src="item"
							mode="aspectFill"
							@click="onPicturePreviewHandler(repairPictureList, index)"
							@longpress="onImageLongPress(repairPictureList, index)"
						/>
					</view>
				</view>
			</view>
		</view>
		<view class="work-order-detail-history-info">
			<view class="title">历史信息</view>
			<template v-if="isRecordLoading">
				<view class="loading-content">
					<Loading1 size="20" style="color: #397fef; font-weight: bold" />
					<text>加载中...</text>
				</view>
			</template>
			<template v-else>
				<view class="content" v-if="workOrderHistory.length > 0">
					<nut-steps direction="vertical" progress-dot>
						<nut-step v-for="(item, index) in workOrderHistory" :key="index">
							<template #content>
								<view class="step-custom-content">
									<view class="step-text">
										<text>{{ item?.operatorTime || '-' }}，由</text>
										<text style="font-weight: bold">
											&ensp;{{ item.operator || '-' }}&ensp;
										</text>
										<text>
											{{ workRecordBehaviorValue(item.operatorBehavior) }}
										</text>
									</view>
									<view class="step-text" v-if="item?.handleResult">
										<text>结果：</text>
										<text
											:style="{
												color:
													item.handleResult === 0 ? '#f44336' : '#4caf50',
											}"
										>
											{{ item?.handleResult === 0 ? '无法处理' : '已处理' }}
										</text>
									</view>
									<view class="step-text" v-if="item?.remark">
										<text>描述：</text>
										<text>{{ item?.remark || '-' }}</text>
									</view>
									<view
										class="step-images"
										v-if="item?.picture && item.picture.trim() !== ''"
									>
										<view
											v-for="(imgSrc, index) in formatHistoryPictures(
												item.picture,
											)"
											:key="index"
											class="step-image"
										>
											<img
												:src="imgSrc"
												mode="aspectFill"
												@click="
													onPicturePreviewHandler(
														formatHistoryPictures(item.picture),
														index,
													)
												"
												@longpress="
													onImageLongPress(
														formatHistoryPictures(item.picture),
														index,
													)
												"
											/>
										</view>
									</view>
								</view>
							</template>
						</nut-step>
					</nut-steps>
				</view>
				<view v-else class="no-data">暂无历史记录</view>
			</template>
		</view>
	</view>
</template>

<script setup lang="ts">
import { onMounted, ref, computed } from 'vue';
import './index.scss';
import {
	jwCloudSystem_V1_Dictionary_ListWebSelection_GET,
	jwCloudMaintain_V1_Maintain_WorkOrder_Page_GET,
	jwCloudMaintain_V1_Maintain_WorkOrderLife_Query_GET,
} from '@/utils/api';
import { getFullAvatarUrl } from '@/utils/utils';
import { showImageActionSheet } from '@/utils/imageDownload';
import Taro from '@tarojs/taro';

// 引入资源
import { Loading1 } from '@nutui/icons-vue-taro';

// 工单类型列表
const workOrderTypeList: any = ref([]);

// 获取工单类型列表
const getWorkOrderTypeList = async () => {
	try {
		const params = {
			systemCode: 'ims',
			groupCode: 'orderType',
		};
		const res = await jwCloudSystem_V1_Dictionary_ListWebSelection_GET(params);
		workOrderTypeList.value = res;
		console.log('工单类型列表:', workOrderTypeList.value);
	} catch (error) {
		console.error('获取工单类型列表失败:', error);
	}
};

// 工单编号
const workOrderNum = ref('');
onMounted(() => {
	getWorkOrderTypeList();
	// 获取工单编号
	const options = Taro.getCurrentInstance().router?.params;
	if (options?.workOrderNum) {
		workOrderNum.value = options.workOrderNum;
		getWorkOrderDetail();
		getWorkOrderHistory();
	}
});

const workOrderDetail = ref<any>({});
const getWorkOrderDetail = async () => {
	try {
		const res = await jwCloudMaintain_V1_Maintain_WorkOrder_Page_GET({
			workOrderNum: workOrderNum.value,
		});
		if (res.items.length > 0) {
			// workOrderDetail.value = res.items[0];
			//todo 增加维修信息数据
			workOrderDetail.value = {
				...res.items[0],
				// state: 3,
				// maintenanceBeginTime: "2025-07-24 13:52:27",
				// maintenanceEndTime: "2025-07-25 08:35:56",
				// maintenanceMan: "维修人员A",
				// maintenanceResultDescription: "维修成功",
				// maintenancePicture: "/jwFileStoragePrefix/devicetype/2025-07-31/1753951971257-1148339641389432832.png"
			};
			console.log('工单详情', workOrderDetail.value);
		}
	} catch (error) {
		console.error('获取工单详情失败:', error);
	}
};

// 工单历史
const workOrderHistory = ref<any>([]);
const isRecordLoading = ref(true);
const getWorkOrderHistory = async () => {
	isRecordLoading.value = true;
	await new Promise((resolve) => setTimeout(resolve, 500));
	try {
		const res = await jwCloudMaintain_V1_Maintain_WorkOrderLife_Query_GET(workOrderNum.value);
		console.log('工单历史', res);
		workOrderHistory.value = res;
	} catch (error) {
		console.error('获取工单历史失败:', error);
	} finally {
		isRecordLoading.value = false;
	}
};

// 格式化工单类型
const formatWorkType = (type: string) => {
	const item = workOrderTypeList.value.find(
		(item: any) => item.key?.toString() === type?.toString(),
	);
	return item?.value || '-';
};

// 格式化上报图片
const reportPictureList = computed(() => {
	const photo = workOrderDetail.value?.reportPicture;
	if (photo && photo.trim()) {
		const imageList = photo
			.split(',')
			.map((photo: string) => getFullAvatarUrl(photo.trim()))
			.filter((url: string) => url);
		return imageList;
	}
	return [];
});

const repairPictureList = computed(() => {
	const photo = workOrderDetail.value?.maintenancePicture;
	if (photo && photo.trim()) {
		const imageList = photo
			.split(',')
			.map((photo: string) => getFullAvatarUrl(photo.trim()))
			.filter((url: string) => url);
		return imageList;
	}
	return [];
});

// 预览图片
const onPicturePreviewHandler = (imageUrls: string[], index: number) => {
	// 使用 Taro 的预览API，
	Taro.previewImage({
		urls: imageUrls,
		current: imageUrls[index],
	});
};

// 图片长按事件处理
const onImageLongPress = (imageUrls: string[], index: number) => {
	showImageActionSheet(imageUrls[index], {
		enableDownload: true,
		enablePreview: true,
		onPreview: () => onPicturePreviewHandler(imageUrls, index),
		onDownloadSuccess: () => {
			console.log('图片保存成功');
		},
		onDownloadError: (error: any) => {
			console.error('图片保存失败:', error);
		},
	});
};

const workRecordBehaviorValue = (val) => {
	const behaviorMap = {
		0: '添加工单',
		1: '更新工单',
		2: '指派工单',
		3: '处理工单',
		4: '关闭工单',
		5: '工单升级',
		6: '投诉建议新增工单',
		7: '打印工单',
		8: '异常关闭',
		9: '修改预约时间',
	};

	return behaviorMap[val] || '未知操作';
};

// 格式化历史记录图片
const formatHistoryPictures = (photo: string) => {
	if (photo && photo.trim()) {
		const imageList = photo
			.split(',')
			.map((photo: string) => getFullAvatarUrl(photo.trim()))
			.filter((url: string) => url);
		return imageList;
	}
	return [];
};

// 0-待指派,1-待处理,2-已处理,3-已关闭,4-异常
const workOrderState = (state: number) => {
	switch (state) {
		case 0:
			return { value: 0, text: '待指派' };
		case 1:
			return { value: 1, text: '待处理' };
		case 2:
			return { value: 2, text: '已处理' };
		case 3:
			return { value: 3, text: '已关闭' };
		case 4:
			return { value: 4, text: '异常' };
		default:
			return { value: -1, text: '未知' };
	}
};

const onRefresh = async () => {
	try {
		await getWorkOrderDetail();
		await getWorkOrderHistory();
		Taro.showToast({
			title: '刷新成功',
			icon: 'success',
		});
	} catch (error) {
		console.error('下拉刷新失败:', error);
		Taro.showToast({
			title: '刷新失败',
			icon: 'error',
		});
	} finally {
		Taro.stopPullDownRefresh();
	}
};

// 使用 Taro 的下拉刷新 hook
Taro.usePullDownRefresh(onRefresh);
</script>
