<template>
	<view class="closed-work-order">
		<view class="work-order-list-header">
			<view class="search-box">
				<Search color="#ccc" />
				<NutInput
					class="search-box-input"
					v-model="searchText"
					clearable
					clear-size="16"
					show-clear-icon
					:border="false"
					placeholder="请输入工单名称"
				/>
				<view class="search-box-btn" @click="getWorkOrderList">搜索</view>
			</view>
			<view class="filter-box">
				<view class="filter-box-date" @click="onDatePickerHandler">
					<Date color="#ff6b35" size="13" />
					<text>{{ dateStr.length ? dateStr : '时间' }}</text>
				</view>
				<view class="filter-box-reset" @click="onResetHandler">
					<Refresh color="grey" size="13" />
					<text>重置</text>
				</view>
			</view>
		</view>
		<view class="work-order-list-content" v-if="workOrderList.length > 0 || isLoading">
			<view
				class="work-order-list-content-item"
				v-for="item in workOrderList"
				:key="item.workOrderNum"
				@click="onItemClickHandler(item)"
			>
				<view class="header">
					<view class="title">{{ item.workOrderName }}</view>
					<view class="status" @click.stop="onStatusClickHandler(item)">关闭工单</view>
				</view>
				<view class="work-order-type">{{ formatWorkType(item.workOrderType) }}</view>
				<view class="maintenance-man">
					<People color="#333" size="13" />
					{{ item?.maintenanceMan || '-（暂未配维修人员）' }}
				</view>
				<view class="footer">
					<view class="appointment">
						<Clock color="#ff6b35" size="13" />
						{{ item?.appointmentTime || '-' }}
					</view>
					<text class="report">
						{{
							item?.reportTime
								? timeLineFormat(
										formatTimeToDate(
											item?.reportTime.replaceAll('-', '/'),
											'YYYY/MM/DD HH:mm:ss',
										),
									)
								: '-'
						}}
					</text>
				</view>
			</view>
			<view class="work-order-list-content-item-more" @click="onLoadMore">
				<template v-if="isLoading">
					<Loading1 size="20" style="color: #397fef; font-weight: bold" />
					<text>加载中...</text>
				</template>
				<template v-else>
					<text v-if="isMoreData">加载更多</text>
					<text v-else>没有更多了~</text>
				</template>
			</view>
		</view>
		<nut-empty
			v-else
			class="work-order-list-content-empty"
			description="暂无工单数据"
		></nut-empty>
		<NutPopup v-model:visible="datePickerModal.isShow" position="bottom">
			<view class="calendar-header">
				<text class="calendar-header-title">{{ datePickerModal.title }}</text>
				<view class="calendar-header-close" @click="datePickerModal.isShow = false">
					关闭
				</view>
				<view class="calendar-header-confirm" @click="onRangeConfirmHandler">确定</view>
				<text class="calendar-header-date">
					{{
						datePickerModal.dateStrRange.length
							? datePickerModal.dateStrRange[0] +
								' ~ ' +
								datePickerModal.dateStrRange[1]
							: '日期范围'
					}}
				</text>
			</view>
			<nut-calendar-card
				v-model="datePickerModal.dateRange"
				type="range"
				@change="chooseRangeTime"
			></nut-calendar-card>
		</NutPopup>
	</view>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import './index.scss';
import { useAreaStore } from '@/stores';
import { formatDate, timeLineFormat, formatTimeToDate } from '@/utils/utils';
import { isWorkOrderAdmin } from '@/utils/role';

import {
	jwCloudSystem_V1_Dictionary_ListWebSelection_GET,
	jwCloudMaintain_V1_Maintain_WorkOrder_Page_GET,
	jwCloudMaintain_V1_Maintain_WorkOrder_Close_POST,
} from '@/utils/api';
import Taro from '@tarojs/taro';

// 引入资源
import { Search, Date, Refresh, Clock, People, Loading1 } from '@nutui/icons-vue-taro';

// 使用 Pinia store
const areaStore = useAreaStore();
const currentArea = computed(() => areaStore.getCurrentArea());

onMounted(() => {
	getWorkOrderTypeList();
	getWorkOrderList();
});

// 工单类型列表
const workOrderTypeList: any = ref([]);

// 获取工单类型列表
const getWorkOrderTypeList = async () => {
	try {
		const params = {
			systemCode: 'ims',
			groupCode: 'orderType',
		};
		const res = await jwCloudSystem_V1_Dictionary_ListWebSelection_GET(params);
		workOrderTypeList.value = res;
		console.log('工单类型列表:', workOrderTypeList.value);
	} catch (error) {
		console.error('获取工单类型列表失败:', error);
	}
};

// 工单列表
const workOrderList: any = ref([]);

// 获取工单列表
const getWorkOrderList = async () => {
	if (!isRefreshing.value) {
		isLoading.value = true;
	}
	await new Promise((resolve) => setTimeout(resolve, 500));
	try {
		const params: any = {
			pageNum: 1,
			pageSize: pageSize * currentPage.value,
			workOrderName: searchText.value,
			areaId: currentArea.value?.areaId,
			state: 2,
			currentUserWorkOrder: !isWorkOrderAdmin(),
		};
		if (dateRange.value.length === 2) {
			const startTime = formatDate(dateRange.value[0], 'YYYY-MM-DD') + ' 00:00:00';
			const endTime = formatDate(dateRange.value[1], 'YYYY-MM-DD') + ' 23:59:59';
			params.reportBeginTime = startTime;
			params.reportEndTime = endTime;
		}
		console.log('params:', params);
		// 调用后端接口
		const res = await jwCloudMaintain_V1_Maintain_WorkOrder_Page_GET(params);
		workOrderList.value = res.items;
		console.log('工单列表:', workOrderList.value);
		total.value = res.total;
	} catch (error) {
		console.error('获取工单列表失败:', error);
	} finally {
		isLoading.value = false;
	}
};

// 搜索文本
const searchText = ref('');

// 时间筛选
const dateStr = ref('');
const dateRange: any = ref([]);

// 日期选择器
const datePickerModal: any = reactive({
	isShow: false,
	title: '请选择日期范围',
	dateStrRange: [],
	dateRange: [],
});

// 日期选择器
const onDatePickerHandler = () => {
	datePickerModal.isShow = true;
	if (dateRange.value.length === 2) {
		datePickerModal.dateRange = dateRange.value;
		datePickerModal.dateStrRange = [
			formatDate(dateRange.value[0], 'YYYY-MM-DD'),
			formatDate(dateRange.value[1], 'YYYY-MM-DD'),
		];
	} else {
		datePickerModal.dateRange = [];
		datePickerModal.dateStrRange = [];
	}
};

// 日期范围选择
const chooseRangeTime = (value: any) => {
	console.log('选择的日期:', value);
	if (value.length === 2) {
		datePickerModal.dateStrRange = [
			formatDate(value[0], 'YYYY-MM-DD'),
			formatDate(value[1], 'YYYY-MM-DD'),
		];
	}
};

// 确认选择时间
const onRangeConfirmHandler = () => {
	dateStr.value =
		formatDate(datePickerModal.dateRange[0], 'MM-DD') +
		' ~ ' +
		formatDate(datePickerModal.dateRange[1], 'MM-DD');
	dateRange.value = datePickerModal.dateRange;
	datePickerModal.isShow = false;
};

const onResetHandler = () => {
	searchText.value = '';
	dateStr.value = '';
	dateRange.value = [];
	datePickerModal.dateStrRange = [];
	datePickerModal.dateRange = [];
	currentPage.value = 1;
	getWorkOrderList();
};

const onItemClickHandler = (item: any) => {
	console.log('点击工单:', item);
	Taro.navigateTo({
		url: `/pagesB/workOrder/workOrderDetail/index?workOrderNum=${item.workOrderNum}`,
	});
};

// 格式化工单类型
const formatWorkType = (type: string) => {
	const item = workOrderTypeList.value.find(
		(item: any) => item.key?.toString() === type?.toString(),
	);
	return item?.value || '-';
};

const pageSize = 20;
const currentPage = ref(1);
const total = ref(0);

// 加载更多
const isLoading = ref(false);
const isMoreData = computed(() => {
	return currentPage.value * pageSize < total.value;
});
// 加载更多
const onLoadMore = () => {
	if (currentPage.value * pageSize >= total.value) {
		return;
	}
	currentPage.value++;
	getWorkOrderList();
};

const isRefreshing = ref(false);
// 下拉刷新处理
const onRefresh = async (isReloadPage: boolean = false) => {
	console.log('开始下拉刷新');
	currentPage.value = 1;
	isRefreshing.value = true;
	try {
		await new Promise((resolve) => setTimeout(resolve, 500));
		await getWorkOrderList();
		if (!isReloadPage) {
			Taro.showToast({
				title: '刷新成功',
				icon: 'success',
			});
		}
	} catch (error) {
		console.error('下拉刷新失败:', error);
		Taro.showToast({
			title: '刷新失败',
			icon: 'error',
		});
	} finally {
		isRefreshing.value = false;
		Taro.stopPullDownRefresh();
	}
};

// 使用 Taro 的下拉刷新 hook
Taro.usePullDownRefresh(onRefresh);

const onStatusClickHandler = (item: any) => {
	console.log('点击工单状态:', item);
	Taro.showModal({
		title: '提示',
		content: `确定要关闭工单“${item.workOrderName}”吗？`,
		success: async (res) => {
			if (res.confirm) {
				try {
					await jwCloudMaintain_V1_Maintain_WorkOrder_Close_POST({
						workOrderNum: item.workOrderNum,
						flag: 1,
					});
					Taro.showToast({
						title: '关闭成功',
						icon: 'success',
					});
					onRefresh(true);
				} catch (error) {
					console.error('关闭工单失败:', error);
				}
			}
		},
	});
};
</script>
