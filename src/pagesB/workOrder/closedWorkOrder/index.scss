.closed-work-order {
  background-color: #fff;
	display: flex;
	flex-direction: column;
	.work-order-list-header {
		position: relative;
		width: 100%;
		display: flex;
		flex-direction: column;
		gap: 5px;
		padding: 0 30px;
		box-sizing: border-box;
		.search-box {
			width: 100%;
			height: 80px;
			padding: 0 20px;
			border-radius: 15px;
			box-sizing: border-box;
			border: 1px solid #ccc;
			display: flex;
			align-items: center;
			justify-content: center;
			gap: 20px;
			.search-box-input {
				flex: 1;
			}
			.search-box-btn {
				color: #397fef;
				font-size: 30px;
				position: relative;
				width: 90px;
				margin-left: 20px;
				&::before {
					content: '|';
					position: absolute;
					color: #ccc;
					left: -30px;
					top: 50%;
					transform: translateY(-50%);
				}
			}
		}
		.filter-box {
			width: 100%;
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 10px 0;
			font-size: 24px;
			.filter-box-date {
				display: flex;
				align-items: center;
				justify-content: center;
				gap: 10px;
				color: #ff6b35;
				padding: 5px 15px;
				background-color: #f0f5fa;
				border-radius: 10px;
			}
			.filter-box-reset {
				display: flex;
				align-items: center;
				justify-content: center;
				gap: 10px;
				color: grey;
				padding: 5px 15px;
				background-color: #f0f5fa;
				border-radius: 10px;
			}
		}
	}

	.work-order-list-content {
		flex: 1;
		overflow-y: auto;
		padding: 0 30px;
		box-sizing: border-box;
		display: flex;
		flex-direction: column;
		gap: 20px;
		.work-order-list-content-item {
			border-bottom: 2px solid #f5f6f7;
			padding: 30px 0;
			display: flex;
			flex-direction: column;
			gap: 10px;
			text {
				font-size: 28px;
				color: #333;
			}
			.header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				.title {
					font-size: 36px;
					font-weight: bold;
					flex: 1;
				}
				.status {
					font-size: 28px;
					padding: 5px 15px;
					font-weight: 500;
					border-radius: 10px;
					margin-left: 20px;
					color: #fff;
					background-color: #f44336;
				}
			}

			.work-order-type {
				font-size: 28px;
				color: #397fef;
				font-weight: 500;
			}

			.maintenance-man {
				display: flex;
				align-items: center;
				gap: 10px;
				font-size: 28px;
				color: #333;
			}

			.footer {
				display: flex;
				justify-content: space-between;
				align-items: center;
				font-size: 28px;
				.appointment {
					display: flex;
					align-items: center;
					gap: 10px;
					color: #ff6b35;
				}
				.report {
					color: #999;
				}
			}
		}
		.work-order-list-content-item-more {
			width: 100%;
			box-sizing: border-box;
			padding: 10px;
			display: flex;
			justify-content: center;
			align-items: center;
			gap: 20px;
			font-size: 28px;
			color: #333;
		}
	}

	.work-order-list-content-empty {
    text-align: center;
		padding: 200px 0;
		font-size: 32px;
		color: #999;
	}

  // nut的input输入框
	.nut-input {
		padding: 0;
		margin: 0;
	}
	.input-text {
		color: grey;
		font-size: 30px;
	}

	.calendar-header {
		padding: 30px 0;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		position: relative;
		background-color: #fff;
		.calendar-header-title {
			font-size: 32px;
			font-weight: bold;
			color: #333;
			display: flex;
			justify-content: center;
			align-items: center;
			// padding: 30px 0;
		}
		.calendar-header-close {
			position: absolute;
			left: 30px;
			top: 55px;
			transform: translateY(-50%);
			font-size: 32px;
			color: #333;
			font-size: normal;
		}

		.calendar-header-confirm {
			position: absolute;
			right: 30px;
			top: 55px;
			transform: translateY(-50%);
			font-size: 32px;
			color: #397fef;
			font-weight: bold;
		}

		.calendar-header-date {
			font-size: 24px;
			color: #333;
			padding-top: 30px;
		}
	}

	// 设置 NutUI Calendar 组件的 CSS 变量
	--nut-calendar-primary-color: #397fef;
	--nut-calendar-day67-font-color: #397fef;

	// 确认按钮相关的变量
	--nut-button-primary-background-color: #397fef;
	--nut-calendar-choose-background-color: rgba(57, 127, 239, 0.1);

	// 设置 NutUI 组件的 CSS 变量
	--nut-picker-confirm-color: #397fef;
	--nut-primary-color: #397fef;
}
