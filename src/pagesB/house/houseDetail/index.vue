<template>
  <view class="house-detail">
    <view class="header">
      <view class="header-left">
        <view class="icon">
          {{ roomInfo.gatehouseNo }}
        </view>
        <view class="header-content">
          <view class="title">
            {{ roomInfo?.term }}期 {{ roomInfo?.buildingNo }}幢 {{ roomInfo?.unit }}单元
            {{ roomInfo?.gatehouseNo }}室
          </view>
          <view class="sub-title">{{ roomInfo?.houseTypeName }}（{{ livingTypeMap[roomInfo?.livingType || 9] }}）</view>
        </view>
      </view>
      <view class="header-right" v-if="roomInfo?.houseStatus !== 1">
        <nut-switch v-model="isFrozen" active-color="#397fef"  :disabled="isInitFrozen" @change="onFrozenChange"/>
      </view>
    </view>
    <view class="fee-info">
      <view class="fee-info-title">
        <text>租赁空间及费用</text>
        <text v-if="!isConfigPrice" style="color: red; font-size:14px">（该房间未配价）</text>
      </view>
      <view class="fee-info-content">
        <view class="discount-box">
          <view class="discount-item">
            <view class="item-title">年付</view>
            <view class="item-desc">（{{ roomPriceConfig?.housePriceTemplateFangZu?.payDiscountYear || '-' }}）</view>
            <view class="item-value">{{ computedPrice(4) }}</view>
          </view>
          <view class="discount-item">
            <view class="item-title">半年付</view>
            <view class="item-desc">（{{ roomPriceConfig?.housePriceTemplateFangZu?.payDiscountHalfYear || '-' }}）</view>
            <view class="item-value">{{ computedPrice(3) }}</view>
          </view>
          <view class="discount-item">
            <view class="item-title">季付</view>
            <view class="item-desc">（{{ roomPriceConfig?.housePriceTemplateFangZu?.payDiscountSeason || '-' }}）</view>
            <view class="item-value">{{ computedPrice(2) }}</view>
          </view>
          <view class="discount-item">
            <view class="item-title">月付</view>
            <view class="item-desc">（{{ roomPriceConfig?.housePriceTemplateFangZu?.payDiscountMonth || '-' }}）</view>
            <view class="item-value">{{ computedPrice(1) }}</view>
          </view>
        </view>
        <view class="fee-item">
          <text class="fee-label">租金价格</text>
          <view class="fee-value">
            <text class="fee-value-title" style="color: #ff6b35">
              {{ formatMoneyWithCommas(roomPriceConfig?.housePriceTemplateFangZu?.price || 0, 2) }}元/月
            </text>
          </view>
        </view>
        <view class="fee-item">
          <view class="fee-label">房间面积</view>
          <view class="fee-value">
            <text class="fee-value-title">
              {{ roomInfo.area || '-' }}㎡
            </text>
          </view>
        </view>
        <view class="fee-item" v-if="roomPriceConfig?.housePriceTemplateGeRenYaJin?.isEnable">
          <view class="fee-label">个人押金</view>
          <view class="fee-value">
            <text class="fee-value-title" style="color: #559f53">
              押{{
                numberToChinese(
                  roomPriceConfig?.housePriceTemplateGeRenYaJin
                    ?.number || 1,
                )
              }}
            </text>
          </view>
        </view>
        <view class="fee-item" v-if="roomPriceConfig?.housePriceTemplateQiYeYaJin?.isEnable">
          <view class="fee-label">企业押金</view>
          <view class="fee-value">
            <text class="fee-value-title" style="color: #559f53">
              押{{
                numberToChinese(
                  roomPriceConfig?.housePriceTemplateQiYeYaJin
                    ?.number || 1,
                )
              }}
            </text>
          </view>
        </view>
        <view class="fee-item" v-if="roomPriceConfig?.housePriceTemplateFuWuFei?.isEnable">
          <view class="fee-label">服务费</view>
          <view class="fee-value">
            <text class="fee-value-title">
              {{
                roomPriceConfig?.housePriceTemplateFuWuFei
                  ?.payMethod
              }}
            </text>
            <text class="fee-value-subtitle">
              ¥
              {{
                formatMoneyWithCommas(
                  roomPriceConfig?.housePriceTemplateFuWuFei
                    ?.price || 0,
                  2,
                )
              }}/月
            </text>
          </view>
        </view>
        <view class="fee-item" v-if="roomPriceConfig?.housePriceTemplateWuYeFei?.isEnable">
          <view class="fee-label">物业费</view>
          <view class="fee-value">
            <text class="fee-value-title">
              {{
                roomPriceConfig?.housePriceTemplateWuYeFei
                  ?.payMethod
              }}
            </text>
            <text class="fee-value-subtitle">
              ¥
              {{
                formatMoneyWithCommas(
                  roomPriceConfig?.housePriceTemplateWuYeFei
                    ?.price || 0,
                  2,
                )
              }}/月
            </text>
          </view>
        </view>
        <view class="fee-item" v-if="
          roomPriceConfig?.housePriceTemplateWangLuoAnZhuangFei
            ?.isEnable
        ">
          <view class="fee-label">网络安装费</view>
          <view class="fee-value">
            <text class="fee-value-title" style="color: #559f53">
              {{
                formatMoneyWithCommas(
                  roomPriceConfig
                    ?.housePriceTemplateWangLuoAnZhuangFei
                    ?.price || 0,
                  2,
                )
              }}元
            </text>
          </view>
        </view>
        <view class="fee-item" v-if="roomPriceConfig?.housePriceTemplateKuanDaiFei?.isEnable">
          <view class="fee-label">宽带费</view>
          <view class="fee-value">
            <text class="fee-value-title">
              {{
                roomPriceConfig?.housePriceTemplateKuanDaiFei
                  ?.payMethod
              }}
              ({{
                roomPriceConfig?.housePriceTemplateKuanDaiFei
                  ?.baoZhangMethod || '-'
              }})
            </text>
            <text class="fee-value-subtitle">
              ¥
              {{
                formatMoneyWithCommas(
                  roomPriceConfig?.housePriceTemplateKuanDaiFei
                    ?.price || 0,
                  2,
                )
              }}/月
            </text>
          </view>
        </view>
        <view class="fee-item" v-if="roomPriceConfig?.housePriceTemplateShuiFei?.isEnable">
          <view class="fee-label">水费</view>
          <view class="fee-value">
            <text class="fee-value-title">
              {{
                roomPriceConfig?.housePriceTemplateShuiFei
                  ?.payMethod
              }}
              ({{
                roomPriceConfig?.housePriceTemplateShuiFei
                  ?.chuZhangMethod || '-'
              }})
            </text>
            <text class="fee-value-subtitle">
              ¥
              {{
                formatMoneyWithCommas(
                  roomPriceConfig?.housePriceTemplateShuiFei
                    ?.price || 0,
                  2,
                )
              }}/吨
            </text>
          </view>
        </view>
        <view class="fee-item" v-if="roomPriceConfig?.housePriceTemplateReShuiFei?.isEnable">
          <view class="fee-label">热水费</view>
          <view class="fee-value">
            <text class="fee-value-title">
              {{
                roomPriceConfig?.housePriceTemplateReShuiFei
                  ?.payMethod
              }}
              ({{
                roomPriceConfig?.housePriceTemplateReShuiFei
                  ?.chuZhangMethod || '-'
              }})
            </text>
            <text class="fee-value-subtitle">
              ¥
              {{
                formatMoneyWithCommas(
                  roomPriceConfig?.housePriceTemplateReShuiFei
                    ?.price || 0,
                  2,
                )
              }}/吨
            </text>
          </view>
        </view>
        <view class="fee-item" v-if="roomPriceConfig?.housePriceTemplateDianFei?.isEnable">
          <view class="fee-label">电费</view>
          <view class="fee-value">
            <text class="fee-value-title">
              {{
                roomPriceConfig?.housePriceTemplateDianFei
                  ?.payMethod
              }}
              ({{
                roomPriceConfig?.housePriceTemplateDianFei
                  ?.chuZhangMethod || '-'
              }})
            </text>
            <text class="fee-value-subtitle">
              ¥
              {{
                formatMoneyWithCommas(
                  roomPriceConfig?.housePriceTemplateDianFei
                    ?.price || 0,
                  2,
                )
              }}/度
            </text>
          </view>
        </view>
      </view>
    </view>

    <view class="tenant-info">
      <view class="tenant-info-header">
        <view class="tenant-info-title">最近房客</view>
        <view class="tenant-info-view-all">
          <text>查看全部</text>
          <RectRight size="13" color="grey" />
        </view>
      </view>
    </view>

    <view class="func-info">
      <view class="func-info-title">常用功能</view>
      <view class="func-info-content">
        <view class="func-info-item" v-for="item in funcList" :key="item.key" @click="onFuncItemClick(item.path)">
          <view class="icon">
            <image :src="item.icon" mode="aspectWidth" />
          </view>
          <view class="text">{{ item.title }}</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { onMounted, ref, computed, watch } from 'vue';
import './index.scss';
import {
  formatMoneyWithCommas,
  numberToChinese
} from '@/utils/utils';
import {
  jwCloudPedestal_V1_Room_List_GET,
  jwAppApi_V1_House_FreezeOrThaw_POST
} from '@/utils/api';
import Taro from '@tarojs/taro';

// 引入资源
import HouseContractPNG from '@/assets/images/house_contract.png';
import HouseBillPNG from '@/assets/images/house_bill.png';
import HouseHydropowerPNG from '@/assets/images/house_hydropower.png';
import HouseLockerPNG from '@/assets/images/house_locker.png';
import HouseAssetsPNG from '@/assets/images/house_assets.png';
import { RectRight } from '@nutui/icons-vue-taro';

// 初始化冻结状态
const isInitFrozen = ref(true);
// 是否冻结房屋
const isFrozen: any = ref();
// 监听冻结状态变化
watch(isFrozen, async (newVal) => {
  console.log('isFrozen:', newVal);
  if (isInitFrozen.value) return;
  try {
    await jwAppApi_V1_House_FreezeOrThaw_POST({
      houseId: houseId.value,
      operate: newVal ? -1 : 0,
    });
    Taro.showToast({
      title: `房屋${newVal ? '冻结' : '解冻'}成功`,
      icon: 'success',
    });
    // 刷新父页面数据
    const pages = Taro.getCurrentPages();
    const prevPage = pages[pages.length - 2]; // 获取A页面实例
    if (prevPage.reloadPage && typeof prevPage.reloadPage === 'function') {
      // 延迟返回，让用户看到处理状态
      setTimeout(() => {
        prevPage.reloadPage();
      }, 1000);
    }
  } catch (error) {
    Taro.showToast({
      title: `房屋${newVal ? '冻结' : '解冻'}失败`,
      icon: 'error',
    });
    console.error('操作失败:', error);
  } finally {
    getHouseDetail();
  }
})

const onFrozenChange = async (val) => {
  if (isInitFrozen.value) return;
  console.log('isFrozen:', val);
  try {
    await jwAppApi_V1_House_FreezeOrThaw_POST({
      houseId: houseId.value,
      operate: val ? -1 : 0,
    });
    Taro.showToast({
      title: `房屋${val ? '冻结' : '解冻'}成功`,
      icon: 'success',
    });
    // 刷新父页面数据
    const pages = Taro.getCurrentPages();
    const prevPage = pages[pages.length - 2]; // 获取A页面实例
    if (prevPage.reloadPage && typeof prevPage.reloadPage === 'function') {
      // 延迟返回，让用户看到处理状态
      setTimeout(() => {
        prevPage.reloadPage();
      }, 1000);
    }
  } catch (error) {
    Taro.showToast({
      title: `房屋${val ? '冻结' : '解冻'}失败`,
      icon: 'error',
    });
    console.error('操作失败:', error);
  } finally {
    getHouseDetail();
  }

}

const houseId = ref('');
onMounted(() => {
  const instance = Taro.getCurrentInstance();
  const params = instance.router?.params;
  if (params?.houseId) {
    houseId.value = params.houseId;
  }
  if (params?.houseStatus) {
    isFrozen.value = params.houseStatus === '0' ? false : true;
    isInitFrozen.value = false;
  }
  console.log('params:', params);
  getHouseDetail();
});

const roomInfo = ref<any>({});
// 房间价格配置
const roomPriceConfig = ref<any>({});
const getHouseDetail = async () => {
  try {
    const params = {
      houseId: houseId.value,
    };
    const res = await jwCloudPedestal_V1_Room_List_GET(params);
    if (res.length === 0) {
      throw new Error('未找到房间信息');
    }
    roomInfo.value = res[0];
    isFrozen.value = roomInfo.value.houseStatus === 0 ? false : true;
    console.log('初始化isFrozen.value:', isFrozen.value);
    console.log('房间信息:', roomInfo.value);
    try {
      roomPriceConfig.value = JSON.parse(res[0].housePriceTemplateBaseStr);
      // 房间价格配置
      if (
        roomPriceConfig.value.housePriceTemplateFangZu &&
        roomPriceConfig.value.housePriceTemplateFangZu.price
      ) {
        paymentMethods.value.forEach((paymentMethod) => {
          paymentMethod.price = roomPriceConfig.value.housePriceTemplateFangZu.price;
        });
        paymentMethods.value[0].rentDiscount =
          roomPriceConfig.value.housePriceTemplateFangZu?.payDiscountYear || 1;
        paymentMethods.value[1].rentDiscount =
          roomPriceConfig.value.housePriceTemplateFangZu?.payDiscountHalfYear || 1;
        paymentMethods.value[2].rentDiscount =
          roomPriceConfig.value.housePriceTemplateFangZu?.payDiscountSeason || 1;
        paymentMethods.value[3].rentDiscount =
          roomPriceConfig.value.housePriceTemplateFangZu?.payDiscountMonth || 1;
      }
      console.log('房间价格配置:', roomPriceConfig.value);
    } catch (error) {
      console.error('解析房间价格配置失败:', error);
    }
  } catch (error) {
    console.error('获取房间详情失败:', error);
  }
};

const isConfigPrice = computed(() => {
  if (!roomPriceConfig.value.housePriceTemplateFangZu) {
    return false;
  }
  if (!roomPriceConfig.value.housePriceTemplateFangZu?.price) {
    return false;
  }
  if (!roomPriceConfig.value.housePriceTemplateFangZu?.payDiscountYear) {
    return false;
  }
  if (!roomPriceConfig.value.housePriceTemplateFangZu?.payDiscountHalfYear) {
    return false;
  }
  if (!roomPriceConfig.value.housePriceTemplateFangZu?.payDiscountSeason) {
    return false;
  }
  if (!roomPriceConfig.value.housePriceTemplateFangZu?.payDiscountMonth) {
    return false;
  }
  return true;
});

// 支付方式
const paymentMethods = ref([
  { label: '付十二', value: 4, disabled: false, rentDiscount: 1, price: 0 },
  { label: '付六', value: 3, disabled: false, rentDiscount: 1, price: 0 },
  { label: '付三', value: 2, disabled: false, rentDiscount: 1, price: 0 },
  { label: '付一', value: 1, disabled: false, rentDiscount: 1, price: 0 },
]);

const computedPrice = (payType) => {
  // 根据支付方式，计算出实际的租金价格
  const paymentItem = paymentMethods.value.find(
    (item) => item.value === payType,
  );
  if (!paymentItem) {
    return 0;
  }
  return formatMoneyWithCommas(paymentItem.price * paymentItem.rentDiscount, 2);
}

// 居住房屋类型
const livingTypeMap = {
  1: '自住房',
  2: '出租房',
  3: '空置房',
  4: '带查房',
  9: '其他',
}

const funcList = ref([
  {
    title: '合同',
    icon: HouseContractPNG,
    path: '/pagesB/house/houseContact/index',
    key: 'contract',
  },
  {
    title: '账单',
    icon: HouseBillPNG,
    path: '/pagesB/house/houseBill/index',
    key: 'bill',
  },
  {
    title: '水电',
    icon: HouseHydropowerPNG,
    path: '/pagesB/house/houseHydropower/index',
    key: 'hydropower',
  },
  {
    title: '智能门锁',
    icon: HouseLockerPNG,
    path: '/pagesB/house/houseLocker/index',
    key: 'locker',
  },
  {
    title: '资产',
    icon: HouseAssetsPNG,
    path: '/pagesB/house/houseAssets/index',
    key: 'assets',
  }
]);

const onFuncItemClick = (path: string) => {
  if (!path) {
    return;
  }
  Taro.navigateTo({
    url: path,
  });
}





</script>
