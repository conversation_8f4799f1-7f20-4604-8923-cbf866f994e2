.house-detail {
	height: 100vh;
	background-color: #f5f6f7;
	.header {
		background-color: #fff;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 30px;
		box-sizing: border-box;
		border-bottom: 1px solid #f0f0f0;
		.header-left {
			flex: 1;
			display: flex;
			align-items: center;
			gap: 20px;
			.icon {
				width: 80px;
				height: 80px;
				border-radius: 50%;
				background-color: #397fef;
				color: #fff;
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 30px;
			}
			.header-content {
				flex: 1;
				height: 100%;
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: flex-start;
				gap: 5px;
				.title {
					font-size: 30px;
					line-height: 1.2;
				}
				.sub-title {
					font-size: 28px;
					color: grey;
				}
			}
		}
		.header-right {
			margin-left: 20px;
		}
	}

	.fee-info {
		padding: 20px 30px;
		box-sizing: border-box;
		background-color: #fff;
		.fee-info-title {
			font-size: 36px;
			font-weight: 500;
			color: #333;
			display: flex;
			align-items: center;
			margin-bottom: 20px;
			&:before {
				content: '';
				display: inline-block;
				width: 8px;
				height: 40px;
				background-color: #397fef;
				border-radius: 5px;
				margin-right: 10px;
			}
		}
		.fee-info-content {
			.fee-item {
				width: 100%;
				border-bottom: 2px solid #f5f6f7;
				padding: 30px 0;
				height: 50px;
				display: flex;
				align-items: center;
				justify-content: space-between;
				gap: 20px;

				.fee-label {
					font-size: 28px;
					color: #333;
				}

				.fee-value {
					display: flex;
					flex-direction: column;
					align-items: flex-end;
					.fee-value-title {
						font-size: 28px;
						color: #333;
						// 太长使用省略号，限制显示一行，超出显示省略号
						-webkit-line-clamp: 1;
						line-clamp: 1;
						overflow: hidden;
						text-overflow: ellipsis;
						display: -webkit-box;
						-webkit-box-orient: vertical;
					}
					.fee-value-subtitle {
						font-size: 24px;
						color: #999;
					}
				}
			}
		}
	}

	.discount-box {
		box-sizing: border-box;
		display: flex;
		flex-wrap: wrap;
		gap: 20px;
		.discount-item {
			flex: 1;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			padding: 10px;
			border-radius: 10px;
			box-shadow: 2px 2px 2px 2px rgba(0, 0, 0, 0.1);
			.item-title {
				font-size: 28px;
				font-weight: 500;
				color: #333;
			}
			.item-desc {
				font-size: 24px;
				color: #999;
			}
			.item-value {
				font-size: 30px;
				font-weight: 500;
				color: #ff6b35;
			}
		}
	}

	.tenant-info {
		padding: 20px 30px;
		box-sizing: border-box;
		background-color: #fff;
		.tenant-info-header {
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-bottom: 10px;
			.tenant-info-title {
				font-size: 36px;
				font-weight: 500;
				color: #333;
				display: flex;
				align-items: center;
				&:before {
					content: '';
					display: inline-block;
					width: 8px;
					height: 40px;
					background-color: #397fef;
					border-radius: 5px;
					margin-right: 10px;
				}
			}
			.tenant-info-view-all {
				background: pink;
				display: flex;
				align-items: center;
				justify-content: center;
				gap: 5px;
				font-size: 28px;
				color: grey;
			}
		}
	}

	.func-info {
		padding: 20px 30px;
		box-sizing: border-box;
		background-color: #fff;
		.func-info-title {
			font-size: 36px;
			font-weight: 500;
			color: #333;
			display: flex;
			align-items: center;
			margin-bottom: 20px;
			&:before {
				content: '';
				display: inline-block;
				width: 8px;
				height: 40px;
				background-color: #397fef;
				border-radius: 5px;
				margin-right: 10px;
			}
		}
		.func-info-content {
			// 一行分成4等分，每个item占1份
			display: grid;
			grid-template-columns: repeat(4, 1fr);
			gap: 15px;
			.func-info-item {
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				gap: 5px;
				padding: 10px;
				color: #333;
				font-size: 28px;
				.icon {
					width: 60px;
					height: 60px;
					border-radius: 50%;
					background-color: #fff;
					color: #397fef;
					display: flex;
					align-items: center;
					justify-content: center;
					font-size: 30px;
					image {
						width: 100%;
						height: 100%;
					}
				}
			}
		}
	}
}
