export default {
	// 主包页面路径列表（包含 tabBar 页面）
	pages: ['pages/launch/index', 'pages/login/index', 'pages/index/index', 'pages/message/index'],
	// 分包结构配置（用于其他功能页面）
	subPackages: [
		// 可以在这里添加其他功能模块的分包
		// 二级页面
		{
			root: 'pagesA',
			pages: [
				'profile/index',
				'house/index',
				'tenant/index',
				'visit/index',
				'assignWorkOrder/index',
			],
		},
		// 三级页面以及以下页面
		{
			root: 'pagesB',
			pages: [
				'resetPwd/index',
        'house/houseDetail/index',
				'house/houseContract/index',
        'house/houseBill/index',
        'house/houseHydropower/index',
        'house/houseLocker/index',
        'house/houseAssets/index',
        'house/houseTenants/index',
				'tenantDetail/index',
				'refundRent/index',
				'visitDetail/index',
				'workOrder/allWorkOrder/index',
				'workOrder/todoWorkOrder/index',
				'workOrder/dealWorkOrder/index',
				'workOrder/closedWorkOrder/index',
				'workOrder/workOrderDetail/index',
			],
		},
	],

	// 底部 tab 栏的表现
	tabBar: {
		color: '#d4d4d8',
		selectedColor: '#397fef',
		backgroundColor: '#FFFFFF',
		list: [
			{
				pagePath: 'pages/index/index',
				text: '首页',
				iconPath: './assets/tab/home.png',
				selectedIconPath: './assets/tab/home_active.png',
			},
			{
				pagePath: 'pages/message/index',
				text: '消息',
				iconPath: './assets/tab/message.png',
				selectedIconPath: './assets/tab/message_active.png',
			},
		],
	},
	window: {
		backgroundTextStyle: 'light',
		navigationBarBackgroundColor: '#fff',
		navigationBarTitleText: 'WeChat',
		navigationBarTextStyle: 'black',
	},
	networkTimeout: {
		request: 60000,
		connectSocket: 60000,
		uploadFile: 60000,
		downloadFile: 60000,
	},
	__usePrivacyCheck__: true,
	useExtendedLib: {
		weui: true,
	},
};
